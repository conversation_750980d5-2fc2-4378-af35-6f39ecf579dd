# -*- coding: utf-8 -*-
"""
主程序入口，负责编排整个视频硬字幕提取流程。
"""

import os
import sys
import argparse
from typing import Dict, Any

# 将项目根目录添加到Python路径中
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.config.config_manager import load_config, get_default_config
from src.video.video_processor import VideoProcessor
from src.image.image_preprocessor import ImagePreprocessor
from src.ocr.tesseract_ocr import TesseractOcr
from src.subtitle.subtitle_item import SubtitleSequence
from src.subtitle.subtitle_processor import SubtitleProcessor
from src.subtitle.srt_generator import SrtGenerator
from src.utils.file_utils import ensure_directory_exists


def main(config: Dict[str, Any]):
    """
    主函数，执行视频字幕提取的核心流程。

    Args:
        config (Dict[str, Any]): 配置字典。
    """
    video_path = config['video']['path']
    output_dir = config['output']['directory']
    ensure_directory_exists(output_dir)

    # 1. 视频处理
    print("开始处理视频...")
    video_path=input("请输入视频路径：").replace('"','')
    video_processor = VideoProcessor(video_path)
    frame_paths = video_processor.extract_frames(
        output_dir=os.path.join(output_dir, 'frames'),
        interval_ms=config['video']['process_interval']
    )
    print(f"视频处理完成，提取了 {len(frame_paths)} 帧图像。")

    # 2. OCR识别
    print("开始进行OCR文字识别...")
    ocr_engine = TesseractOcr(config['ocr'])
    subtitle_sequence = SubtitleSequence()

    for i, frame_path in enumerate(frame_paths):
        # 从文件名解析时间戳 (ms)
        try:
            timestamp_ms = float(os.path.basename(frame_path).split('_')[1].split('.')[0])
        except (IndexError, ValueError):
            print(f"警告：无法从文件名 {frame_path} 中解析时间戳，已跳过此帧。")
            continue

        # 3. 图像预处理
        img_config = config.get('image_processing', {})
        preprocessor = ImagePreprocessor(frame_path)

        # 应用裁剪
        crop_area = config.get('video', {}).get('crop_area')
        if crop_area and all(k in crop_area for k in ['x', 'y', 'width', 'height']):
            preprocessor.crop(crop_area)

        # 应用灰度化
        if img_config.get('grayscale'):
            preprocessor.to_grayscale()

        # 应用二值化
        if 'binary_threshold' in img_config:
            preprocessor.binarize(img_config['binary_threshold'])

        # 应用降噪
        if img_config.get('denoise'):
            preprocessor.denoise()

        # 应用对比度增强
        if 'contrast_enhancement' in img_config:
            preprocessor.enhance_contrast(img_config['contrast_enhancement'])
        
        # 应用缩放
        if 'resize_factor' in img_config and img_config['resize_factor'] != 1.0:
            preprocessor.resize(img_config['resize_factor'])

        # 应用模糊
        if 'blur_kernel_size' in img_config and img_config['blur_kernel_size'] > 0:
            preprocessor.blur(img_config['blur_kernel_size'])

        # 应用形态学操作
        morph_ops = img_config.get('morphology_operations', {})
        if morph_ops.get('enabled'):
            preprocessor.apply_morphology(
                kernel_size=morph_ops.get('kernel_size', 2),
                iterations=morph_ops.get('iterations', 1)
            )

        processed_image = preprocessor.get_image()

        text = ocr_engine.recognize_text(processed_image)
        if text:
            # 估算结束时间
            start_time_sec = timestamp_ms / 1000.0
            # 假设字幕持续到下一帧或一个默认间隔
            if i + 1 < len(frame_paths):
                next_timestamp_ms = float(os.path.basename(frame_paths[i+1]).split('_')[1].split('.')[0])
                end_time_sec = next_timestamp_ms / 1000.0
            else:
                end_time_sec = start_time_sec + (config['video']['process_interval'] / 1000.0)
            
            subtitle_sequence.add_item(start_time_sec, end_time_sec, text)
        
        if (i + 1) % 10 == 0:
            print(f"已处理 {i + 1}/{len(frame_paths)} 帧...")

    print(f"OCR识别完成，初步生成 {len(subtitle_sequence)} 条字幕。")

    # 4. 字幕后处理
    print("开始进行字幕后处理...")
    processor = SubtitleProcessor(subtitle_sequence)
    merged_sequence = processor.merge_similar_subtitles(
        similarity_threshold=config['subtitle']['similarity_threshold'],
        time_threshold=config['subtitle']['merge_time_threshold']
    )
    print(f"字幕后处理完成，合并后剩余 {len(merged_sequence)} 条字幕。")

    # 5. 生成SRT文件
    print("开始生成SRT文件...")
    srt_generator = SrtGenerator(merged_sequence)
    output_filename = os.path.splitext(os.path.basename(video_path))[0] + '.srt'
    output_path = os.path.join(output_dir, output_filename)
    srt_generator.save_to_file(output_path)
    print(f"SRT文件已生成: {output_path}")


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="视频硬字幕提取工具")
    parser.add_argument("-c", "--config", help="配置文件路径")
    args = parser.parse_args()

    if args.config:
        app_config = load_config(args.config)
    else:
        print("未提供配置文件，将使用默认配置。")
        app_config = get_default_config()

    main(app_config)