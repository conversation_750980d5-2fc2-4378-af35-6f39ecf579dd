import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk
import threading
import time
import os
from typing import Optional, Tuple, Dict, Any
import yaml
from pathlib import Path

# 导入视频处理模块
try:
    from .video_processor import extract_and_crop_frame
except ImportError:
    from video_processor import extract_and_crop_frame

class VideoPlayerWidget:
    """
    视频播放器GUI组件，专门用于字幕区域选择和预览。
    
    主要功能：
    - 视频文件加载与播放控制
    - 可拖拽的进度条，支持精确定位
    - 鼠标拖拽选择字幕区域
    - 返回基于原始视频分辨率的坐标
    """
    
    def __init__(self, parent=None, config=None):
        """
        初始化视频播放器组件。
        
        Args:
            parent: 父窗口，如果为None则创建独立窗口
            config (dict): 配置参数字典
        """
        self.parent = parent
        # 先设置临时配置，然后加载默认配置并合并
        self._temp_config = config
        self.config = self._load_default_config()
        
        # 视频相关属性
        self.video_capture = None
        self.video_path = None
        self.total_frames = 0
        self.fps = 0
        self.duration = 0
        self.current_frame_number = 0
        self.video_width = 0
        self.video_height = 0
        
        # 显示相关属性
        self.display_width = 0
        self.display_height = 0
        self.scale_factor_x = 1.0
        self.scale_factor_y = 1.0
        
        # 字幕区域选择相关
        self.selection_start = None
        self.selection_end = None
        self.selection_rect = None
        self.is_selecting = False
        self.subtitle_area = None  # (x, y, width, height) 基于原始分辨率
        
        # 截帧相关属性
        self.is_extracting = False
        self.extraction_timer = None
        self.extraction_start_time = 0
        self.extraction_output_dir = None
        self.extraction_count = 0
        
        # GUI组件
        self.root = None
        self.canvas = None
        self.progress_var = None
        self.progress_scale = None
        self.time_label = None
        self.photo = None
        
        # 支持的视频格式
        self.supported_formats = ('.mp4', '.avi', '.mkv', '.mov', '.wmv')
        
        self._setup_gui()
    
    def _load_default_config(self) -> Dict[str, Any]:
        """
        加载默认配置参数。
        
        Returns:
            dict: 默认配置字典
        """
        default_config = {
            'window_width': 800,
            'window_height': 600,
            'min_window_width': 600,
            'min_window_height': 400,
            'canvas_bg': 'black',
            'selection_color': 'red',
            'selection_width': 2,
            'supported_formats': ['.mp4', '.avi', '.mkv', '.mov', '.wmv']
        }
        
        # 尝试从配置文件加载
        config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'default_config.yaml')
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    file_config = yaml.safe_load(f)
                    if 'video_player' in file_config:
                        default_config.update(file_config['video_player'])
            except Exception as e:
                print(f"加载配置文件失败: {e}")
        
        # 如果传入了自定义配置，合并到默认配置中
        if hasattr(self, '_temp_config') and self._temp_config:
            default_config.update(self._temp_config)
        
        return default_config
    
    def _setup_gui(self):
        """
        设置GUI界面。
        """
        if self.parent is None:
            self.root = tk.Tk()
            self.root.title("视频播放器 - 字幕区域选择")
            # 设置窗口大小和最小尺寸（仅当创建新窗口时）
            self.root.geometry(f"{self.config['window_width']}x{self.config['window_height']}")
            self.root.minsize(self.config['min_window_width'], self.config['min_window_height'])
            # 创建主框架
            main_frame = ttk.Frame(self.root)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            self.container = main_frame
        else:
            # 当有父容器时，直接使用父容器作为主框架
            self.container = self.parent
            self.root = self.parent.winfo_toplevel()  # 获取顶级窗口引用
            main_frame = self.container
        
        # 创建工具栏
        self._create_toolbar(main_frame)
        
        # 创建视频显示区域
        self._create_video_canvas(main_frame)
        
        # 创建控制区域
        self._create_controls(main_frame)
        
        # 绑定窗口调整事件（仅当是独立窗口时）
        if self.parent is None:
            self.root.bind('<Configure>', self._on_window_resize)
    
    def _create_toolbar(self, parent):
        """
        创建工具栏。
        
        Args:
            parent: 父容器
        """
        toolbar = ttk.Frame(parent)
        if self.parent is None:
            toolbar.pack(fill=tk.X, pady=(0, 5))
        else:
            toolbar.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 加载视频按钮
        load_btn = ttk.Button(toolbar, text="加载视频", command=self._load_video_dialog)
        load_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 清除选择按钮
        clear_btn = ttk.Button(toolbar, text="清除选择", command=self._clear_selection)
        clear_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 截帧按钮
        self.extract_btn = ttk.Button(toolbar, text="开始截帧", command=self._toggle_frame_extraction)
        self.extract_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.extract_btn.config(state='disabled')  # 初始状态为禁用
        
        # 状态标签
        self.status_label = ttk.Label(toolbar, text="请加载视频文件")
        self.status_label.pack(side=tk.LEFT, padx=(10, 0))
    
    def _create_video_canvas(self, parent):
        """
        创建视频显示画布。
        
        Args:
            parent: 父容器
        """
        canvas_frame = ttk.Frame(parent)
        if self.parent is None:
            canvas_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        else:
            canvas_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 5))
            parent.rowconfigure(1, weight=1)
            parent.columnconfigure(0, weight=1)
        
        self.canvas = tk.Canvas(
            canvas_frame,
            bg=self.config['canvas_bg'],
            highlightthickness=0
        )
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 绑定鼠标事件用于区域选择
        self.canvas.bind('<Button-1>', self._on_mouse_press)
        self.canvas.bind('<B1-Motion>', self._on_mouse_drag)
        self.canvas.bind('<ButtonRelease-1>', self._on_mouse_release)
    
    def _create_controls(self, parent):
        """
        创建控制区域。
        
        Args:
            parent: 父容器
        """
        controls_frame = ttk.Frame(parent)
        if self.parent is None:
            controls_frame.pack(fill=tk.X)
        else:
            controls_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        # 时间显示
        self.time_label = ttk.Label(controls_frame, text="00:00 / 00:00")
        self.time_label.pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_scale = ttk.Scale(
            controls_frame,
            from_=0,
            to=100,
            orient=tk.HORIZONTAL,
            variable=self.progress_var,
            command=self._on_progress_change
        )
        self.progress_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        # 坐标显示
        self.coord_label = ttk.Label(controls_frame, text="选择区域: 未选择")
        self.coord_label.pack(side=tk.RIGHT)
    
    def _load_video_dialog(self):
        """
        打开文件对话框加载视频。
        """
        filetypes = [
            ('视频文件', ' '.join(f'*{ext}' for ext in self.supported_formats)),
            ('所有文件', '*.*')
        ]
        
        file_path = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=filetypes
        )
        
        if file_path:
            self.load_video(file_path)
    
    def load_video(self, video_path: str) -> bool:
        """
        加载视频文件。
        
        Args:
            video_path (str): 视频文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            # 验证文件格式
            if not any(video_path.lower().endswith(ext) for ext in self.supported_formats):
                messagebox.showerror("错误", f"不支持的视频格式。支持的格式: {', '.join(self.supported_formats)}")
                return False
            
            # 验证文件存在
            if not os.path.exists(video_path):
                messagebox.showerror("错误", "视频文件不存在")
                return False
            
            # 释放之前的视频
            if self.video_capture:
                self.video_capture.release()
            
            # 打开新视频
            self.video_capture = cv2.VideoCapture(video_path)
            if not self.video_capture.isOpened():
                messagebox.showerror("错误", "无法打开视频文件")
                return False
            
            # 获取视频信息
            self.video_path = video_path
            self.fps = self.video_capture.get(cv2.CAP_PROP_FPS)
            self.total_frames = int(self.video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
            self.duration = self.total_frames / self.fps if self.fps > 0 else 0
            self.video_width = int(self.video_capture.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.video_height = int(self.video_capture.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # 重置状态
            self.current_frame_number = 0
            self.subtitle_area = None
            self._clear_selection()
            
            # 更新进度条范围
            self.progress_scale.configure(to=self.total_frames - 1)
            
            # 显示第一帧
            self._show_frame(0)
            
            # 更新状态
            filename = os.path.basename(video_path)
            self.status_label.config(text=f"已加载: {filename} ({self.video_width}x{self.video_height})")
            
            # 启用截帧按钮
            if self.extract_btn:
                self.extract_btn.config(state='normal')
            
            return True
            
        except Exception as e:
            messagebox.showerror("错误", f"加载视频失败: {str(e)}")
            return False
    
    def _show_frame(self, frame_number: int):
        """
        显示指定帧。
        
        Args:
            frame_number (int): 帧号
        """
        if not self.video_capture:
            return
        
        try:
            # 设置帧位置
            self.video_capture.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            ret, frame = self.video_capture.read()
            
            if not ret:
                return
            
            self.current_frame_number = frame_number
            
            # 转换颜色空间
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # 计算显示尺寸
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            
            if canvas_width <= 1 or canvas_height <= 1:
                # 画布还未初始化，延迟显示
                self.root.after(100, lambda: self._show_frame(frame_number))
                return
            
            # 计算缩放比例（保持宽高比）
            scale_x = canvas_width / self.video_width
            scale_y = canvas_height / self.video_height
            scale = min(scale_x, scale_y)
            
            self.display_width = int(self.video_width * scale)
            self.display_height = int(self.video_height * scale)
            
            # 计算缩放因子（用于坐标转换）
            self.scale_factor_x = self.display_width / self.video_width
            self.scale_factor_y = self.display_height / self.video_height
            
            # 调整图像大小
            frame_resized = cv2.resize(frame_rgb, (self.display_width, self.display_height))
            
            # 转换为PIL图像
            pil_image = Image.fromarray(frame_resized)
            self.photo = ImageTk.PhotoImage(pil_image)
            
            # 清除画布并显示图像
            self.canvas.delete("all")
            
            # 计算居中位置
            x_offset = (canvas_width - self.display_width) // 2
            y_offset = (canvas_height - self.display_height) // 2
            
            self.canvas.create_image(
                x_offset, y_offset,
                anchor=tk.NW,
                image=self.photo,
                tags="video_frame"
            )
            
            # 重新绘制选择框
            self._redraw_selection(x_offset, y_offset)
            
            # 更新时间显示
            current_time = frame_number / self.fps if self.fps > 0 else 0
            self._update_time_display(current_time)
            
            # 更新进度条
            self.progress_var.set(frame_number)
            
        except Exception as e:
            print(f"显示帧时出错: {e}")
    
    def _redraw_selection(self, x_offset: int, y_offset: int):
        """
        重新绘制选择框。
        
        Args:
            x_offset (int): X偏移量
            y_offset (int): Y偏移量
        """
        if self.subtitle_area:
            x, y, width, height = self.subtitle_area
            
            # 转换到显示坐标
            display_x = x * self.scale_factor_x + x_offset
            display_y = y * self.scale_factor_y + y_offset
            display_width = width * self.scale_factor_x
            display_height = height * self.scale_factor_y
            
            self.canvas.create_rectangle(
                display_x, display_y,
                display_x + display_width, display_y + display_height,
                outline=self.config['selection_color'],
                width=self.config['selection_width'],
                tags="selection"
            )
    
    def _update_time_display(self, current_time: float):
        """
        更新时间显示。
        
        Args:
            current_time (float): 当前时间（秒）
        """
        def format_time(seconds):
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            secs = int(seconds % 60)
            
            if hours > 0:
                return f"{hours:02d}:{minutes:02d}:{secs:02d}"
            else:
                return f"{minutes:02d}:{secs:02d}"
        
        current_str = format_time(current_time)
        total_str = format_time(self.duration)
        self.time_label.config(text=f"{current_str} / {total_str}")
    
    def _on_progress_change(self, value):
        """
        进度条变化事件处理。
        
        Args:
            value: 进度条值
        """
        if not self.video_capture:
            return
        
        frame_number = int(float(value))
        self._show_frame(frame_number)
    
    def _on_mouse_press(self, event):
        """
        鼠标按下事件处理。
        
        Args:
            event: 鼠标事件
        """
        if not self.video_capture:
            return
        
        # 检查点击是否在视频区域内
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        x_offset = (canvas_width - self.display_width) // 2
        y_offset = (canvas_height - self.display_height) // 2
        
        if (x_offset <= event.x <= x_offset + self.display_width and
            y_offset <= event.y <= y_offset + self.display_height):
            
            self.is_selecting = True
            self.selection_start = (event.x - x_offset, event.y - y_offset)
            self.selection_end = self.selection_start
            
            # 清除之前的选择
            self.canvas.delete("selection_preview")
    
    def _on_mouse_drag(self, event):
        """
        鼠标拖拽事件处理。
        
        Args:
            event: 鼠标事件
        """
        if not self.is_selecting or not self.video_capture:
            return
        
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        x_offset = (canvas_width - self.display_width) // 2
        y_offset = (canvas_height - self.display_height) // 2
        
        # 限制在视频区域内
        x = max(0, min(event.x - x_offset, self.display_width))
        y = max(0, min(event.y - y_offset, self.display_height))
        
        self.selection_end = (x, y)
        
        # 绘制预览矩形
        self.canvas.delete("selection_preview")
        
        x1, y1 = self.selection_start
        x2, y2 = self.selection_end
        
        # 确保矩形坐标正确
        left = min(x1, x2) + x_offset
        top = min(y1, y2) + y_offset
        right = max(x1, x2) + x_offset
        bottom = max(y1, y2) + y_offset
        
        self.canvas.create_rectangle(
            left, top, right, bottom,
            outline=self.config['selection_color'],
            width=self.config['selection_width'],
            dash=(5, 5),
            tags="selection_preview"
        )
    
    def _on_mouse_release(self, event):
        """
        鼠标释放事件处理。
        
        Args:
            event: 鼠标事件
        """
        if not self.is_selecting or not self.video_capture:
            return
        
        self.is_selecting = False
        
        # 计算最终选择区域
        if self.selection_start and self.selection_end:
            x1, y1 = self.selection_start
            x2, y2 = self.selection_end
            
            # 确保坐标正确
            left = min(x1, x2)
            top = min(y1, y2)
            right = max(x1, x2)
            bottom = max(y1, y2)
            
            # 检查选择区域大小
            if abs(right - left) > 5 and abs(bottom - top) > 5:
                # 转换到原始视频坐标
                orig_x = int(left / self.scale_factor_x)
                orig_y = int(top / self.scale_factor_y)
                orig_width = int((right - left) / self.scale_factor_x)
                orig_height = int((bottom - top) / self.scale_factor_y)
                
                # 确保坐标在视频范围内
                orig_x = max(0, min(orig_x, self.video_width - 1))
                orig_y = max(0, min(orig_y, self.video_height - 1))
                orig_width = min(orig_width, self.video_width - orig_x)
                orig_height = min(orig_height, self.video_height - orig_y)
                
                self.subtitle_area = (orig_x, orig_y, orig_width, orig_height)
                
                # 更新坐标显示
                self.coord_label.config(
                    text=f"选择区域: ({orig_x}, {orig_y}, {orig_width}, {orig_height})"
                )
                
                # 清除预览并绘制最终选择
                self.canvas.delete("selection_preview")
                self.canvas.delete("selection")
                
                canvas_width = self.canvas.winfo_width()
                canvas_height = self.canvas.winfo_height()
                x_offset = (canvas_width - self.display_width) // 2
                y_offset = (canvas_height - self.display_height) // 2
                
                self._redraw_selection(x_offset, y_offset)
            else:
                # 选择区域太小，清除
                self.canvas.delete("selection_preview")
    
    def _clear_selection(self):
        """
        清除选择区域。
        """
        self.subtitle_area = None
        self.selection_start = None
        self.selection_end = None
        self.canvas.delete("selection")
        self.canvas.delete("selection_preview")
        self.coord_label.config(text="选择区域: 未选择")
    
    def _on_window_resize(self, event):
        """
        窗口大小调整事件处理。
        
        Args:
            event: 窗口事件
        """
        if event.widget == self.root and self.video_capture:
            # 延迟重新显示当前帧以适应新尺寸
            self.root.after(100, lambda: self._show_frame(self.current_frame_number))
    
    def get_subtitle_area(self) -> Optional[Tuple[int, int, int, int]]:
        """
        获取用户选择的字幕区域坐标。
        
        Returns:
            tuple: (x, y, width, height) 基于原始视频分辨率，如果未选择则返回None
        """
        return self.subtitle_area
    
    def set_position(self, timestamp: float) -> bool:
        """
        设置播放位置到指定时间戳。
        
        Args:
            timestamp (float): 时间戳（秒）
            
        Returns:
            bool: 设置是否成功
        """
        if not self.video_capture or timestamp < 0 or timestamp > self.duration:
            return False
        
        frame_number = int(timestamp * self.fps)
        frame_number = max(0, min(frame_number, self.total_frames - 1))
        
        self._show_frame(frame_number)
        return True
    
    def get_current_frame(self) -> Optional[np.ndarray]:
        """
        获取当前显示的视频帧。
        
        Returns:
            np.ndarray: 当前帧的图像数据，如果无视频则返回None
        """
        if not self.video_capture:
            return None
        
        self.video_capture.set(cv2.CAP_PROP_POS_FRAMES, self.current_frame_number)
        ret, frame = self.video_capture.read()
        
        return frame if ret else None
    
    def get_current_timestamp(self) -> float:
        """
        获取当前时间戳。
        
        Returns:
            float: 当前时间戳（秒）
        """
        if not self.video_capture or self.fps <= 0:
            return 0.0
        
        return self.current_frame_number / self.fps
    
    def run(self):
        """
        运行GUI主循环（仅在独立窗口模式下使用）。
        """
        if self.parent is None and self.root:
            self.root.mainloop()
    
    def _toggle_frame_extraction(self):
        """
        切换截帧状态（开始/停止）。
        """
        if self.is_extracting:
            self._stop_frame_extraction()
        else:
            self._start_frame_extraction()
    
    def _start_frame_extraction(self):
        """
        开始自动截帧。
        """
        if not self.video_capture or not self.video_path:
            messagebox.showerror("错误", "请先加载视频文件")
            return
        
        # 检查是否有选择的字幕区域
        subtitle_area = self.get_subtitle_area()
        if not subtitle_area:
            messagebox.showwarning("警告", "请先选择字幕区域再开始截帧")
            return
        
        try:
            # 创建输出目录
            video_name = Path(self.video_path).stem
            video_dir = Path(self.video_path).parent
            self.extraction_output_dir = video_dir / video_name
            self.extraction_output_dir.mkdir(exist_ok=True)
            
            # 初始化截帧状态
            self.is_extracting = True
            self.extraction_start_time = self.get_current_timestamp()
            self.extraction_count = 0
            
            # 更新按钮状态
            self.extract_btn.config(text="停止截帧", state='normal')
            
            # 禁用其他控制按钮
            self._set_controls_state(False)
            
            # 开始定时器（1秒间隔）
            self._schedule_next_extraction()
            
            # 更新状态显示
            self.status_label.config(text=f"正在截帧... 已截取: {self.extraction_count} 帧")
            
        except Exception as e:
            messagebox.showerror("错误", f"开始截帧失败: {str(e)}")
            self._stop_frame_extraction()
    
    def _stop_frame_extraction(self):
        """
        停止自动截帧。
        """
        self.is_extracting = False
        
        # 取消定时器
        if self.extraction_timer:
            self.root.after_cancel(self.extraction_timer)
            self.extraction_timer = None
        
        # 恢复按钮状态
        self.extract_btn.config(text="开始截帧", state='normal')
        
        # 启用其他控制按钮
        self._set_controls_state(True)
        
        # 显示完成信息
        if self.extraction_count > 0:
            messagebox.showinfo("截帧完成", 
                              f"截帧完成！\n共截取 {self.extraction_count} 帧\n保存位置: {self.extraction_output_dir}")
            self.status_label.config(text=f"截帧完成，共 {self.extraction_count} 帧")
        else:
            filename = os.path.basename(self.video_path) if self.video_path else "视频"
            self.status_label.config(text=f"已加载: {filename}")
    
    def _schedule_next_extraction(self):
        """
        安排下一次截帧。
        """
        if not self.is_extracting:
            return
        
        # 1秒后执行截帧
        self.extraction_timer = self.root.after(1000, self._extract_current_frame)
    
    def _extract_current_frame(self):
        """
        截取当前帧。
        """
        if not self.is_extracting or not self.video_capture:
            return
        
        try:
            current_timestamp = self.get_current_timestamp()
            
            # 检查是否到达视频结尾
            if current_timestamp >= self.duration:
                self._stop_frame_extraction()
                return
            
            # 获取字幕区域坐标
            subtitle_area = self.get_subtitle_area()
            if not subtitle_area:
                self._stop_frame_extraction()
                messagebox.showerror("错误", "字幕区域丢失，停止截帧")
                return
            
            # 准备裁剪坐标
            crop_coordinates = {
                'x': subtitle_area[0],
                'y': subtitle_area[1], 
                'width': subtitle_area[2],
                'height': subtitle_area[3]
            }
            
            # 调用截帧函数
            result = extract_and_crop_frame(
                video_path=self.video_path,
                timestamp=current_timestamp,
                crop_coordinates=crop_coordinates,
                output_dir=str(self.extraction_output_dir),
                filename_prefix="frame"
            )
            
            if result:
                self.extraction_count += 1
                # 更新状态显示
                self.status_label.config(text=f"正在截帧... 已截取: {self.extraction_count} 帧")
                
                # 前进1秒
                next_timestamp = current_timestamp + 1.0
                if next_timestamp < self.duration:
                    self.set_position(next_timestamp)
                    # 安排下一次截帧
                    self._schedule_next_extraction()
                else:
                    # 到达视频结尾
                    self._stop_frame_extraction()
            else:
                # 截帧失败，继续尝试下一帧
                next_timestamp = current_timestamp + 1.0
                if next_timestamp < self.duration:
                    self.set_position(next_timestamp)
                    self._schedule_next_extraction()
                else:
                    self._stop_frame_extraction()
                    
        except Exception as e:
            messagebox.showerror("错误", f"截帧过程中发生错误: {str(e)}")
            self._stop_frame_extraction()
    
    def _set_controls_state(self, enabled: bool):
        """
        设置控制按钮的启用/禁用状态。
        
        Args:
            enabled (bool): True为启用，False为禁用
        """
        state = 'normal' if enabled else 'disabled'
        
        # 禁用/启用进度条
        if hasattr(self, 'progress_scale'):
            self.progress_scale.config(state=state)
    
    def destroy(self):
        """
        销毁组件并释放资源。
        """
        # 停止截帧
        if self.is_extracting:
            self._stop_frame_extraction()
        
        if self.video_capture:
            self.video_capture.release()
        
        if self.root and self.parent is None:
            self.root.destroy()


def main():
    """
    主函数，用于测试VideoPlayerWidget。
    """
    # 创建测试窗口
    player = VideoPlayerWidget()
    
    # 添加测试按钮
    def test_get_area():
        area = player.get_subtitle_area()
        if area:
            print(f"选择的字幕区域: {area}")
            messagebox.showinfo("选择区域", f"坐标: {area}")
        else:
            messagebox.showinfo("选择区域", "未选择区域")
    
    test_btn = ttk.Button(player.root, text="获取选择区域", command=test_get_area)
    test_btn.pack(side=tk.BOTTOM, pady=5)
    
    # 运行
    player.run()


if __name__ == "__main__":
    main()