# 视频硬字幕提取转换系统

一个基于Python的视频硬字幕自动提取和转换工具，能够从视频文件中识别硬编码字幕并转换为标准SRT字幕文件。

## 🚀 项目特性

- **智能字幕提取**: 自动从视频指定区域提取字幕画面
- **多OCR引擎支持**: 支持Tesseract、PaddleOCR、EasyOCR等多种OCR引擎
- **高精度识别**: 通过图像预处理提高OCR识别准确率
- **智能后处理**: 自动去重、合并连续字幕、优化时间轴
- **标准格式输出**: 生成符合标准的SRT字幕文件
- **模块化设计**: 函数式编程思想，易于扩展和维护
- **配置驱动**: 灵活的YAML配置文件支持
- **性能优化**: 支持批量处理和并发优化

## 📋 系统要求

- Python 3.8+
- OpenCV 4.8+
- 支持的操作系统：Windows、Linux、macOS

## 🛠️ 安装指南

### 1. 克隆项目
```bash
git clone <repository-url>
cd ocr
```

### 2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\\Scripts\\activate   # Windows
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 安装OCR引擎

#### Tesseract OCR
```bash
# Ubuntu/Debian
sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim

# macOS
brew install tesseract tesseract-lang

# Windows
# 下载并安装：https://github.com/UB-Mannheim/tesseract/wiki
```

#### PaddleOCR（可选）
```bash
pip install paddlepaddle paddleocr
```

## 🚀 快速开始

### 基本用法
```bash
python src/main.py --video input.mp4 --output output.srt --config config/default_config.yaml
```

### 自定义配置
```bash
# 复制默认配置
cp config/default_config.yaml config/my_config.yaml

# 编辑配置文件
vim config/my_config.yaml

# 使用自定义配置
python src/main.py --video input.mp4 --output output.srt --config config/my_config.yaml
```

## ⚙️ 配置说明

### 视频处理配置
```yaml
video:
  frame_interval: 1.0    # 帧提取间隔（秒）
  crop_area:             # 字幕区域坐标
    x: 0
    y: 720
    width: 1920
    height: 200
```

### OCR配置
```yaml
ocr:
  provider: "tesseract"        # OCR引擎选择
  language: "chi_sim"          # 识别语言
  confidence_threshold: 0.6    # 置信度阈值
```

### 字幕处理配置
```yaml
subtitle:
  min_duration: 0.5           # 最小字幕持续时间
  duplicate_threshold: 0.8    # 重复检测阈值
  text_cleaning: true         # 启用文本清理
```

## 📁 项目结构

```
ocr/
├── src/                    # 源代码
│   ├── core/              # 核心功能模块
│   │   ├── video_processor.py      # 视频处理
│   │   ├── image_processor.py      # 图像处理
│   │   ├── subtitle_processor.py   # 字幕数据处理
│   │   └── srt_generator.py        # SRT文件生成
│   ├── interfaces/        # 接口抽象层
│   │   ├── ocr_interface.py        # OCR抽象接口
│   │   └── ocr_providers/          # OCR实现
│   ├── utils/             # 工具函数
│   │   ├── config_manager.py       # 配置管理
│   │   ├── file_utils.py          # 文件操作
│   │   └── validation.py          # 数据验证
│   └── main.py            # 主程序入口
├── config/                # 配置文件
├── tests/                 # 测试代码
├── docs/                  # 项目文档
└── requirements.txt       # 依赖清单
```

## 🔧 开发指南

### 运行测试
```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_video_processor.py

# 生成覆盖率报告
pytest --cov=src tests/
```

### 代码格式化
```bash
# 格式化代码
black src/ tests/

# 检查代码风格
flake8 src/ tests/

# 类型检查
mypy src/
```

### 添加新的OCR提供者
1. 在`src/interfaces/ocr_providers/`目录下创建新的提供者文件
2. 继承`OCRProvider`抽象基类
3. 实现必要的接口方法
4. 在配置文件中添加相应配置

## 📊 性能优化

### 内存优化
- 使用流式处理避免一次性加载所有帧
- 及时释放不需要的图像数据
- 配置合理的批处理大小

### 速度优化
- 启用多线程OCR处理
- 使用GPU加速（如果支持）
- 调整图像处理参数

### 配置建议
```yaml
performance:
  max_workers: 4           # 根据CPU核心数调整
  chunk_size: 100          # 根据内存大小调整
  memory_limit_mb: 2048    # 设置内存限制
```

## 🐛 故障排除

### 常见问题

1. **OCR识别准确率低**
   - 调整图像预处理参数
   - 尝试不同的OCR引擎
   - 优化字幕区域坐标

2. **处理速度慢**
   - 增加并发线程数
   - 调整帧提取间隔
   - 使用GPU加速

3. **内存不足**
   - 减少批处理大小
   - 降低图像分辨率
   - 设置内存限制

### 日志分析
```bash
# 查看详细日志
tail -f logs/subtitle_extractor.log

# 启用调试模式
python src/main.py --debug --video input.mp4
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [OpenCV](https://opencv.org/) - 计算机视觉库
- [Tesseract](https://github.com/tesseract-ocr/tesseract) - OCR引擎
- [PaddleOCR](https://github.com/PaddlePaddle/PaddleOCR) - 深度学习OCR工具
- [MoviePy](https://zulko.github.io/moviepy/) - 视频处理库

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 参与讨论
