# -*- coding: utf-8 -*-
"""
定义单个字幕条目的数据结构。
"""

from dataclasses import dataclass, field
from typing import List

@dataclass
class SubtitleItem:
    """
    表示单个字幕条目的数据类。
    """
    start_time: float  # 开始时间（秒）
    end_time: float    # 结束时间（秒）
    text: str          # 字幕文本

    def __post_init__(self):
        if self.start_time < 0 or self.end_time < 0:
            raise ValueError("时间戳不能为负数。")
        if self.start_time > self.end_time:
            raise ValueError("开始时间不能晚于结束时间。")

@dataclass
class SubtitleSequence:
    """
    表示一个完整的字幕序列，包含多个字幕条目。
    """
    items: List[SubtitleItem] = field(default_factory=list)

    def add_item(self, item: SubtitleItem):
        """
        向序列中添加一个新的字幕条目。
        """
        self.items.append(item)

    def sort_by_time(self):
        """
        按开始时间对字幕条目进行排序。
        """
        self.items.sort(key=lambda x: x.start_time)

    def __len__(self) -> int:
        return len(self.items)

    def __iter__(self):
        return iter(self.items)