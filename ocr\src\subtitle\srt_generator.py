# -*- coding: utf-8 -*-
"""
将字幕序列生成为SRT文件格式。
"""

from .subtitle_item import SubtitleSequence

def format_time_for_srt(total_seconds: float) -> str:
    """
    将总秒数格式化为SRT时间戳字符串 (HH:MM:SS,ms)。

    Args:
        total_seconds (float): 总秒数。

    Returns:
        str: 格式化的SRT时间戳字符串。
    """
    assert total_seconds >= 0, "时间不能为负数"
    total_seconds = max(0, total_seconds)
    total_millis = int(total_seconds * 1000 + 0.5)

    millis = total_millis % 1000
    total_seconds_int = total_millis // 1000

    hours = total_seconds_int // 3600
    minutes = (total_seconds_int % 3600) // 60
    seconds = total_seconds_int % 60

    return f"{hours:02d}:{minutes:02d}:{seconds:02d},{millis:03d}"

class SrtGenerator:
    """
    一个用于从SubtitleSequence生成SRT格式内容的类。
    """

    def __init__(self, sequence: SubtitleSequence):
        """
        初始化SrtGenerator。

        Args:
            sequence (SubtitleSequence): 要转换为SRT的字幕序列。
        """
        self.sequence = sequence

    def generate_srt_content(self) -> str:
        """
        生成完整的SRT文件内容字符串。

        Returns:
            str: SRT格式的字符串。
        """
        srt_blocks = []
        for i, item in enumerate(self.sequence.items, 1):
            start_time_str = format_time_for_srt(item.start_time)
            end_time_str = format_time_for_srt(item.end_time)
            
            block = f"{i}\n{start_time_str} --> {end_time_str}\n{item.text}\n"
            srt_blocks.append(block)
            
        return "\n".join(srt_blocks)

    def save_to_file(self, filepath: str):
        """
        将生成的SRT内容保存到文件。

        Args:
            filepath (str): 要保存SRT文件的路径。
        """
        content = self.generate_srt_content()
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)