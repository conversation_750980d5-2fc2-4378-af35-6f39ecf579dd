# 视频硬字幕提取转换系统 - 技术设计文档

## 1. 系统架构

### 1.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   视频输入      │───▶│   帧提取处理    │───▶│   图像预处理    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   SRT输出       │◀───│   字幕数据处理  │◀───│   OCR文字识别   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.2 模块划分
- **core/**: 核心功能模块
- **interfaces/**: 接口抽象层
- **utils/**: 工具函数模块
- **config/**: 配置管理

## 2. 核心模块设计

### 2.1 视频处理模块 (video_processor.py)

```python
def extract_frame_at_time(video_path: str, timestamp: float, crop_area: dict) -> np.ndarray:
    """在指定时间提取视频帧"""
    
def generate_timestamps(duration: float, interval: float) -> List[float]:
    """生成时间戳序列"""
    
def get_video_info(video_path: str) -> dict:
    """获取视频元数据"""
    
def validate_video_file(video_path: str) -> bool:
    """验证视频文件有效性"""
```

### 2.2 图像处理模块 (image_processor.py)

```python
def crop_image(image: np.ndarray, coordinates: dict) -> np.ndarray:
    """裁剪图像到指定区域"""
    
def convert_to_grayscale(image: np.ndarray) -> np.ndarray:
    """转换为灰度图像"""
    
def apply_binary_threshold(image: np.ndarray, threshold: int) -> np.ndarray:
    """应用二值化阈值"""
    
def denoise_image(image: np.ndarray) -> np.ndarray:
    """图像降噪处理"""
    
def enhance_contrast(image: np.ndarray) -> np.ndarray:
    """增强图像对比度"""
```

### 2.3 OCR接口模块 (ocr_interface.py)

```python
class OCRProvider(ABC):
    """OCR提供者抽象基类"""
    
    @abstractmethod
    def recognize_text(self, image: np.ndarray) -> OCRResult:
        """识别图像中的文字"""
        
    @abstractmethod
    def batch_recognize(self, images: List[np.ndarray]) -> List[OCRResult]:
        """批量识别文字"""
        
    def validate_result(self, result: OCRResult) -> bool:
        """验证OCR结果"""
```

### 2.4 字幕处理模块 (subtitle_processor.py)

```python
def clean_text(raw_text: str) -> str:
    """清理和格式化文本"""
    
def detect_duplicate_content(current: str, previous: str, threshold: float) -> bool:
    """检测重复内容"""
    
def merge_consecutive_subtitles(subtitles: List[Subtitle]) -> List[Subtitle]:
    """合并连续字幕"""
    
def validate_subtitle_timing(subtitle: Subtitle) -> bool:
    """验证字幕时间"""
```

### 2.5 SRT生成模块 (srt_generator.py)

```python
def format_timestamp(seconds: float) -> str:
    """格式化时间戳为SRT格式"""
    
def create_subtitle_entry(index: int, start: float, end: float, text: str) -> str:
    """创建SRT字幕条目"""
    
def generate_srt_content(subtitles: List[Subtitle]) -> str:
    """生成完整SRT内容"""
    
def save_srt_file(content: str, filepath: str, encoding: str) -> bool:
    """保存SRT文件"""
```

## 3. 数据结构设计

### 3.1 核心数据类型

```python
@dataclass
class VideoInfo:
    duration: float
    fps: float
    width: int
    height: int
    format: str

@dataclass
class CropArea:
    x: int
    y: int
    width: int
    height: int

@dataclass
class OCRResult:
    text: str
    confidence: float
    bbox: Optional[List[int]]

@dataclass
class Subtitle:
    index: int
    start_time: float
    end_time: float
    text: str
    confidence: float
```

### 3.2 配置数据结构

```python
@dataclass
class VideoConfig:
    frame_interval: float
    crop_area: CropArea

@dataclass
class ImageProcessingConfig:
    grayscale: bool
    binary_threshold: int
    denoise: bool
    contrast_enhancement: float

@dataclass
class OCRConfig:
    provider: str
    language: str
    confidence_threshold: float

@dataclass
class SubtitleConfig:
    min_duration: float
    max_duration: float
    duplicate_threshold: float
    text_cleaning: bool

@dataclass
class OutputConfig:
    encoding: str
    format: str
```

## 4. 主程序流程设计

### 4.1 函数式流程组合

```python
def main(video_path: str, config_path: str, output_path: str) -> bool:
    """主程序入口"""
    return pipe(
        load_and_validate_config(config_path),
        partial(validate_and_get_video_info, video_path),
        partial(generate_processing_plan, video_path),
        process_video_frames,
        post_process_subtitles,
        partial(generate_and_save_srt, output_path)
    )

def process_video_frames(processing_plan: ProcessingPlan) -> List[Subtitle]:
    """处理视频帧的核心流程"""
    return pipe(
        processing_plan.timestamps,
        partial(extract_frames_batch, processing_plan.video_path, processing_plan.config.crop_area),
        partial(preprocess_images_batch, processing_plan.config.image_processing),
        partial(recognize_text_batch, processing_plan.config.ocr),
        partial(create_subtitle_entries, processing_plan.timestamps)
    )
```

### 4.2 错误处理策略

```python
def safe_process_frame(frame_processor: Callable, *args, **kwargs) -> Optional[Any]:
    """安全的帧处理包装器"""
    try:
        return frame_processor(*args, **kwargs)
    except Exception as e:
        logger.error(f"Frame processing failed: {e}")
        return None

def retry_with_backoff(func: Callable, max_retries: int = 3) -> Callable:
    """带退避的重试装饰器"""
    def wrapper(*args, **kwargs):
        for attempt in range(max_retries):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if attempt == max_retries - 1:
                    raise e
                time.sleep(2 ** attempt)
    return wrapper
```

## 5. 性能优化设计

### 5.1 内存优化
- 流式处理，避免一次性加载所有帧
- 及时释放不需要的图像数据
- 使用生成器减少内存占用

### 5.2 并发处理
- OCR识别支持多线程并发
- 图像预处理管道并行化
- 异步I/O操作

### 5.3 缓存策略
- 视频元数据缓存
- 处理结果缓存
- 配置文件缓存

## 6. 扩展接口设计

### 6.1 OCR提供者接口
```python
class TesseractProvider(OCRProvider):
    """Tesseract OCR实现"""
    
class PaddleOCRProvider(OCRProvider):
    """PaddleOCR实现"""
    
class EasyOCRProvider(OCRProvider):
    """EasyOCR实现"""
```

### 6.2 图像处理管道
```python
class ImageProcessor:
    def __init__(self, pipeline: List[Callable]):
        self.pipeline = pipeline
    
    def process(self, image: np.ndarray) -> np.ndarray:
        return reduce(lambda img, func: func(img), self.pipeline, image)
```

### 6.3 GUI接口预留
```python
class SubtitleExtractorAPI:
    """为GUI提供的API接口"""
    
    def extract_subtitles_async(self, video_path: str, config: dict, 
                               progress_callback: Callable) -> Future:
        """异步字幕提取"""
        
    def get_progress(self) -> float:
        """获取处理进度"""
        
    def cancel_extraction(self) -> bool:
        """取消提取操作"""
```

## 7. 测试策略

### 7.1 单元测试
- 每个函数独立测试
- 边界条件测试
- 异常情况测试

### 7.2 集成测试
- 端到端流程测试
- 不同视频格式测试
- 配置参数组合测试

### 7.3 性能测试
- 内存使用监控
- 处理速度基准测试
- 并发性能测试
