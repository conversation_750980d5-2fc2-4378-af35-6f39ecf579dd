# -*- coding: utf-8 -*-
"""
SRT文件生成器模块的单元测试
"""

import pytest
import os
from src.subtitle.subtitle_item import SubtitleItem, SubtitleSequence
from src.subtitle.srt_generator import SrtGenerator, format_time_for_srt

def test_format_time_for_srt():
    """测试时间戳格式化功能"""
    assert format_time_for_srt(0) == "00:00:00,000"
    assert format_time_for_srt(1.2345) == "00:00:01,235" # 测试四舍五入
    assert format_time_for_srt(3661.5) == "01:01:01,500"
    with pytest.raises(AssertionError):
        format_time_for_srt(-1)

@pytest.fixture
def sample_sequence_for_srt() -> SubtitleSequence:
    """创建一个用于SRT生成的样本序列"""
    seq = SubtitleSequence()
    seq.add_item(SubtitleItem(1.5, 3.0, "First line"))
    seq.add_item(SubtitleItem(4.25, 5.75, "Second line\nwith two lines"))
    return seq

def test_generate_srt_content(sample_sequence_for_srt):
    """测试生成SRT内容的功能"""
    generator = SrtGenerator(sample_sequence_for_srt)
    content = generator.generate_srt_content()

    expected_content = (
        "1\n"
        "00:00:01,500 --> 00:00:03,000\n"
        "First line\n"
        "\n"
        "2\n"
        "00:00:04,250 --> 00:00:05,750\n"
        "Second line\nwith two lines\n"
    ).strip()

    # 为了比较，我们将两者都按行分割并去除空行
    assert content.strip().split('\n') == expected_content.split('\n')

def test_save_to_file(sample_sequence_for_srt, tmp_path):
    """测试将SRT内容保存到文件的功能"""
    generator = SrtGenerator(sample_sequence_for_srt)
    filepath = tmp_path / "test.srt"
    generator.save_to_file(str(filepath))

    assert os.path.exists(filepath)

    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    expected_content = generator.generate_srt_content()
    assert content == expected_content