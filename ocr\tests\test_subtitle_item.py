# -*- coding: utf-8 -*-
"""
字幕数据结构模块的单元测试
"""

import pytest
from src.subtitle.subtitle_item import SubtitleItem, SubtitleSequence

def test_subtitle_item_creation():
    """测试SubtitleItem的成功创建"""
    item = SubtitleItem(start_time=1.0, end_time=2.5, text="Hello")
    assert item.start_time == 1.0
    assert item.end_time == 2.5
    assert item.text == "Hello"

def test_subtitle_item_invalid_times():
    """测试SubtitleItem的无效时间戳"""
    with pytest.raises(ValueError, match="时间戳不能为负数"):
        SubtitleItem(start_time=-1.0, end_time=2.0, text="A")

    with pytest.raises(ValueError, match="开始时间不能晚于结束时间"):
        SubtitleItem(start_time=3.0, end_time=2.0, text="B")

def test_subtitle_sequence_creation_and_add():
    """测试SubtitleSequence的创建和添加项目"""
    seq = SubtitleSequence()
    assert len(seq) == 0

    item1 = SubtitleItem(1.0, 2.0, "First")
    seq.add_item(item1)
    assert len(seq) == 1
    assert seq.items[0] == item1

def test_subtitle_sequence_sorting():
    """测试SubtitleSequence的排序功能"""
    seq = SubtitleSequence()
    item1 = SubtitleItem(5.0, 6.0, "Third")
    item2 = SubtitleItem(1.0, 2.0, "First")
    item3 = SubtitleItem(3.0, 4.0, "Second")

    seq.add_item(item1)
    seq.add_item(item2)
    seq.add_item(item3)

    seq.sort_by_time()

    assert seq.items[0].text == "First"
    assert seq.items[1].text == "Second"
    assert seq.items[2].text == "Third"

def test_subtitle_sequence_iteration():
    """测试SubtitleSequence的迭代功能"""
    seq = SubtitleSequence()
    texts = ["A", "B", "C"]
    for i, text in enumerate(texts):
        seq.add_item(SubtitleItem(float(i), float(i+1), text))
    
    collected_texts = [item.text for item in seq]
    assert collected_texts == texts