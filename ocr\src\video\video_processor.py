# -*- coding: utf-8 -*-
"""
视频处理模块，用于从视频中提取帧。
"""

import cv2
import os
from typing import List

from src.utils.file_utils import ensure_directory_exists

class VideoProcessor:
    """
    一个用于处理视频文件的类。
    """

    def __init__(self, video_path: str):
        """
        初始化VideoProcessor。

        Args:
            video_path (str): 视频文件的路径。
        """
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件未找到: {video_path}")
        self.video_path = video_path
        self.cap = cv2.VideoCapture(video_path)
        if not self.cap.isOpened():
            raise IOError(f"无法打开视频文件: {video_path}")

    def get_frame_rate(self) -> float:
        """
        获取视频的帧率。

        Returns:
            float: 视频的帧率。
        """
        return self.cap.get(cv2.CAP_PROP_FPS)

    def extract_frames(
        self, output_dir: str, interval_ms: int = 1000
    ) -> List[str]:
        """
        按指定的时间间隔（毫秒）从视频中提取帧并保存为图像文件。
        采用时间戳跳转的方式，提高效率和精度。

        Args:
            output_dir (str): 保存提取帧的目录。
            interval_ms (int): 提取帧的时间间隔（毫秒）。

        Returns:
            List[str]: 保存的帧图像文件的路径列表。
        """
        ensure_directory_exists(output_dir)
        
        saved_frame_paths = []
        total_duration_ms = self.cap.get(cv2.CAP_PROP_FRAME_COUNT) / self.get_frame_rate() * 1000
        
        current_time_ms = 0
        while current_time_ms < total_duration_ms:
            self.cap.set(cv2.CAP_PROP_POS_MSEC, current_time_ms)
            ret, frame = self.cap.read()
            if not ret:
                break

            frame_filename = os.path.join(output_dir, f"frame_{current_time_ms:.0f}.png")
            cv2.imwrite(frame_filename, frame)
            saved_frame_paths.append(frame_filename)
            
            current_time_ms += interval_ms

        return saved_frame_paths

    def __del__(self):
        """
        释放视频捕获对象。
        """
        if hasattr(self, 'cap') and self.cap.isOpened():
            self.cap.release()