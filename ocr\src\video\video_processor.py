import cv2
import os
import logging

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def extract_and_crop_frame(
    video_path: str,
    timestamp: float,
    crop_coordinates: dict,
    output_dir: str,
    filename_prefix: str = "frame"
) -> str | None:
    """
    从视频文件中提取指定时间点的帧画面，进行裁剪并保存。

    Args:
        video_path (str): 视频文件的完整路径。
        timestamp (float): 提取帧的时间点（以秒为单位）。
        crop_coordinates (dict): 裁剪区域坐标，格式为 {'x': int, 'y': int, 'width': int, 'height': int}。
        output_dir (str): 输出文件夹路径。
        filename_prefix (str, optional): 文件名前缀。默认为 "frame"。

    Returns:
        str | None: 成功时返回保存的文件路径，失败时返回 None。
    """
    # 1. 验证视频文件是否存在
    if not os.path.exists(video_path):
        logging.error(f"视频文件不存在: {video_path}")
        return None

    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        logging.error(f"无法打开视频文件: {video_path}")
        return None

    try:
        # 2. 验证时间点是否在视频时长范围内
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = frame_count / fps

        if not (0 <= timestamp <= duration):
            logging.error(f"时间点 {timestamp}s 超出视频时长范围 [0, {duration:.2f}s]")
            return None

        # 3. 计算目标帧
        target_frame_number = int(timestamp * fps)
        cap.set(cv2.CAP_PROP_POS_FRAMES, target_frame_number)

        ret, frame = cap.read()
        if not ret:
            logging.error(f"无法在时间点 {timestamp}s 读取帧")
            return None

        frame_height, frame_width, _ = frame.shape

        # 4. 验证裁剪坐标是否合理
        x = crop_coordinates.get('x', 0)
        y = crop_coordinates.get('y', 0)
        width = crop_coordinates.get('width', 0)
        height = crop_coordinates.get('height', 0)

        if not (0 <= x < frame_width and 0 <= y < frame_height and x + width <= frame_width and y + height <= frame_height and width > 0 and height > 0):
            logging.error(f"无效的裁剪坐标 {crop_coordinates}，图像尺寸为 {frame_width}x{frame_height}")
            return None

        # 5. 裁剪图像
        cropped_frame = frame[y:y+height, x:x+width]

        # 6. 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 7. 保存文件
        output_filename = f"{filename_prefix}_{timestamp}s.png"
        output_path = os.path.join(output_dir, output_filename)
        
        if cv2.imwrite(output_path, cropped_frame):
            logging.info(f"成功将帧保存到: {output_path}")
            return output_path
        else:
            logging.error(f"无法保存帧到: {output_path}")
            return None

    except Exception as e:
        logging.error(f"处理视频时发生未知错误: {e}")
        return None
    finally:
        cap.release()

if __name__ == "__main__":
    video_path = input('请输入视频路径:').replace('"','')
    timestamp = 10.0
    crop_coordinates = {'x': 100, 'y': 200, 'width': 300, 'height': 100}
    output_dir = ".tests"
    filename_prefix = "cropped_frame"

    result = extract_and_crop_frame(video_path, timestamp, crop_coordinates, output_dir, filename_prefix)
    if result:
        print(f"裁剪成功，保存路径: {result}")
    else:
        print("裁剪失败")
