#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频播放器组件演示脚本

这个脚本展示了如何使用VideoPlayerWidget组件进行字幕区域选择和视频处理。
包含完整的GUI界面和功能演示。

使用方法:
1. 运行脚本: python demo_video_player.py
2. 点击"加载视频"按钮选择视频文件
3. 使用鼠标在视频画面上拖拽选择字幕区域
4. 拖动进度条浏览视频内容
5. 点击"提取当前帧"或"批量提取"按钮进行帧提取
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from src.video.video_player_widget import VideoPlayerWidget
from src.video.video_processor import extract_and_crop_frame

class VideoPlayerDemo:
    """
    视频播放器演示应用程序。
    
    提供完整的GUI界面来演示VideoPlayerWidget的各项功能，
    包括视频加载、区域选择、帧提取等。
    """
    
    def __init__(self):
        """
        初始化演示应用程序。
        """
        self.root = tk.Tk()
        self.root.title("视频播放器组件演示 - 字幕区域选择")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # 状态变量（在创建界面前初始化）
        self.current_video_path = None
        self.output_directory = str(project_root / "output")
        
        # 确保输出目录存在
        os.makedirs(self.output_directory, exist_ok=True)
        
        # 配置样式
        self.setup_styles()
        
        # 创建界面
        self.create_widgets()
    
    def setup_styles(self):
        """
        设置界面样式。
        """
        style = ttk.Style()
        
        # 配置按钮样式
        style.configure('Action.TButton', padding=(10, 5))
        style.configure('Success.TButton', foreground='green')
        style.configure('Warning.TButton', foreground='orange')
        style.configure('Error.TButton', foreground='red')
    
    def create_widgets(self):
        """
        创建界面组件。
        """
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 创建控制面板
        self.create_control_panel(main_frame)
        
        # 创建视频播放器
        self.create_video_player(main_frame)
        
        # 创建信息面板
        self.create_info_panel(main_frame)
        
        # 创建状态栏
        self.create_status_bar(main_frame)
    
    def create_control_panel(self, parent):
        """
        创建控制面板。
        
        Args:
            parent: 父容器
        """
        control_frame = ttk.LabelFrame(parent, text="控制面板", padding="5")
        control_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 文件操作按钮
        ttk.Button(
            control_frame, 
            text="加载视频", 
            command=self.load_video,
            style='Action.TButton'
        ).grid(row=0, column=0, padx=(0, 10))
        
        ttk.Button(
            control_frame, 
            text="清除选择", 
            command=self.clear_selection,
            style='Warning.TButton'
        ).grid(row=0, column=1, padx=(0, 10))
        
        # 分隔符
        ttk.Separator(control_frame, orient='vertical').grid(
            row=0, column=2, sticky=(tk.N, tk.S), padx=10
        )
        
        # 提取操作按钮
        ttk.Button(
            control_frame, 
            text="提取当前帧", 
            command=self.extract_current_frame,
            style='Success.TButton'
        ).grid(row=0, column=3, padx=(0, 10))
        
        ttk.Button(
            control_frame, 
            text="批量提取(每5秒)", 
            command=self.batch_extract_frames,
            style='Success.TButton'
        ).grid(row=0, column=4, padx=(0, 10))
        
        # 分隔符
        ttk.Separator(control_frame, orient='vertical').grid(
            row=0, column=5, sticky=(tk.N, tk.S), padx=10
        )
        
        # 设置按钮
        ttk.Button(
            control_frame, 
            text="设置输出目录", 
            command=self.set_output_directory
        ).grid(row=0, column=6)
    
    def create_video_player(self, parent):
        """
        创建视频播放器组件。
        
        Args:
            parent: 父容器
        """
        # 视频播放器配置
        player_config = {
            'window_width': 800,
            'window_height': 450,
            'canvas_bg': 'black',
            'selection_color': 'red',
            'selection_width': 2
        }
        
        # 创建视频播放器
        self.video_player = VideoPlayerWidget(
            parent=parent, 
            config=player_config
        )
        # VideoPlayerWidget会自动处理布局，不需要手动调用grid
    
    def create_info_panel(self, parent):
        """
        创建信息显示面板。
        
        Args:
            parent: 父容器
        """
        info_frame = ttk.LabelFrame(parent, text="信息面板", padding="5")
        info_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 配置网格
        info_frame.columnconfigure(1, weight=1)
        
        # 视频信息
        ttk.Label(info_frame, text="当前视频:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.video_info_label = ttk.Label(info_frame, text="未加载视频")
        self.video_info_label.grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        # 选择区域信息
        ttk.Label(info_frame, text="选择区域:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.selection_info_label = ttk.Label(info_frame, text="未选择区域")
        self.selection_info_label.grid(row=1, column=1, sticky=(tk.W, tk.E))
        
        # 当前时间信息
        ttk.Label(info_frame, text="当前时间:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10))
        self.time_info_label = ttk.Label(info_frame, text="00:00 / 00:00")
        self.time_info_label.grid(row=2, column=1, sticky=(tk.W, tk.E))
        
        # 输出目录信息
        ttk.Label(info_frame, text="输出目录:").grid(row=3, column=0, sticky=tk.W, padx=(0, 10))
        self.output_info_label = ttk.Label(info_frame, text=self.output_directory)
        self.output_info_label.grid(row=3, column=1, sticky=(tk.W, tk.E))
        
        # 定时更新信息
        self.update_info()
    
    def create_status_bar(self, parent):
        """
        创建状态栏。
        
        Args:
            parent: 父容器
        """
        self.status_var = tk.StringVar()
        self.status_var.set("就绪 - 请加载视频文件开始使用")
        
        status_bar = ttk.Label(
            parent, 
            textvariable=self.status_var, 
            relief=tk.SUNKEN, 
            anchor=tk.W
        )
        status_bar.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def load_video(self):
        """
        加载视频文件。
        """
        # 支持的视频格式
        filetypes = [
            ("视频文件", "*.mp4 *.avi *.mkv *.mov *.wmv"),
            ("MP4文件", "*.mp4"),
            ("AVI文件", "*.avi"),
            ("MKV文件", "*.mkv"),
            ("MOV文件", "*.mov"),
            ("WMV文件", "*.wmv"),
            ("所有文件", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=filetypes,
            initialdir=str(project_root)
        )
        
        if file_path:
            self.status_var.set("正在加载视频...")
            self.root.update()
            
            success = self.video_player.load_video(file_path)
            
            if success:
                self.current_video_path = file_path
                filename = os.path.basename(file_path)
                self.status_var.set(f"视频加载成功: {filename}")
                
                # 更新视频信息
                video_info = f"{filename} ({self.video_player.video_width}x{self.video_player.video_height})"
                self.video_info_label.config(text=video_info)
                
                messagebox.showinfo(
                    "成功", 
                    f"视频加载成功!\n\n"
                    f"文件: {filename}\n"
                    f"分辨率: {self.video_player.video_width}x{self.video_player.video_height}\n"
                    f"时长: {self.format_time(self.video_player.duration)}\n"
                    f"帧率: {self.video_player.fps:.1f} FPS\n\n"
                    f"现在可以在视频画面上拖拽鼠标选择字幕区域。"
                )
            else:
                self.status_var.set("视频加载失败")
                messagebox.showerror("错误", "无法加载视频文件，请检查文件格式是否支持。")
    
    def clear_selection(self):
        """
        清除当前选择。
        """
        if hasattr(self.video_player, '_clear_selection'):
            self.video_player._clear_selection()
            self.status_var.set("已清除选择区域")
        else:
            messagebox.showwarning("警告", "无法清除选择，请检查视频播放器状态。")
    
    def extract_current_frame(self):
        """
        提取当前帧。
        """
        if not self.current_video_path:
            messagebox.showwarning("警告", "请先加载视频文件。")
            return
        
        selection_area = self.video_player.get_subtitle_area()
        if not selection_area:
            messagebox.showwarning("警告", "请先在视频画面上选择字幕区域。")
            return
        
        # 获取当前时间戳
        current_timestamp = self.video_player.get_current_timestamp()
        
        self.status_var.set("正在提取当前帧...")
        self.root.update()
        
        try:
            # 调用提取函数
            output_path = extract_and_crop_frame(
                video_path=self.current_video_path,
                timestamp=current_timestamp,
                crop_coordinates=selection_area,
                output_dir=self.output_directory,
                filename_prefix="current_frame"
            )
            
            if output_path:
                self.status_var.set(f"帧提取成功: {os.path.basename(output_path)}")
                messagebox.showinfo(
                    "成功", 
                    f"当前帧提取成功!\n\n"
                    f"时间戳: {self.format_time(current_timestamp)}\n"
                    f"区域: {selection_area}\n"
                    f"保存路径: {output_path}"
                )
            else:
                self.status_var.set("帧提取失败")
                messagebox.showerror("错误", "帧提取失败，请检查参数设置。")
        
        except Exception as e:
            self.status_var.set("帧提取出错")
            messagebox.showerror("错误", f"帧提取过程中出现错误:\n{str(e)}")
    
    def batch_extract_frames(self):
        """
        批量提取帧（每5秒一帧）。
        """
        if not self.current_video_path:
            messagebox.showwarning("警告", "请先加载视频文件。")
            return
        
        selection_area = self.video_player.get_subtitle_area()
        if not selection_area:
            messagebox.showwarning("警告", "请先在视频画面上选择字幕区域。")
            return
        
        # 确认批量提取
        duration = self.video_player.duration
        frame_count = int(duration // 5) + 1
        
        result = messagebox.askyesno(
            "确认批量提取",
            f"将从视频中每5秒提取一帧，共约 {frame_count} 帧。\n\n"
            f"视频时长: {self.format_time(duration)}\n"
            f"选择区域: {selection_area}\n"
            f"输出目录: {self.output_directory}\n\n"
            f"是否继续？"
        )
        
        if not result:
            return
        
        self.status_var.set("正在批量提取帧...")
        self.root.update()
        
        try:
            success_count = 0
            failed_count = 0
            
            # 每5秒提取一帧
            for i in range(0, int(duration), 5):
                timestamp = float(i)
                
                output_path = extract_and_crop_frame(
                    video_path=self.current_video_path,
                    timestamp=timestamp,
                    crop_coordinates=selection_area,
                    output_dir=self.output_directory,
                    filename_prefix="batch_frame"
                )
                
                if output_path:
                    success_count += 1
                else:
                    failed_count += 1
                
                # 更新进度
                progress = (i + 5) / duration * 100
                self.status_var.set(f"批量提取进度: {progress:.1f}% ({success_count} 成功)")
                self.root.update()
            
            self.status_var.set(f"批量提取完成: {success_count} 成功, {failed_count} 失败")
            
            messagebox.showinfo(
                "批量提取完成",
                f"批量帧提取完成!\n\n"
                f"成功提取: {success_count} 帧\n"
                f"提取失败: {failed_count} 帧\n"
                f"输出目录: {self.output_directory}"
            )
        
        except Exception as e:
            self.status_var.set("批量提取出错")
            messagebox.showerror("错误", f"批量提取过程中出现错误:\n{str(e)}")
    
    def set_output_directory(self):
        """
        设置输出目录。
        """
        directory = filedialog.askdirectory(
            title="选择输出目录",
            initialdir=self.output_directory
        )
        
        if directory:
            self.output_directory = directory
            self.output_info_label.config(text=directory)
            self.status_var.set(f"输出目录已设置: {directory}")
    
    def update_info(self):
        """
        定时更新信息显示。
        """
        try:
            # 更新选择区域信息
            if hasattr(self.video_player, 'get_subtitle_area'):
                selection_area = self.video_player.get_subtitle_area()
                if selection_area:
                    x, y, w, h = selection_area
                    selection_text = f"({x}, {y}) - {w}x{h}"
                else:
                    selection_text = "未选择区域"
                self.selection_info_label.config(text=selection_text)
            
            # 更新时间信息
            if hasattr(self.video_player, 'get_current_timestamp') and hasattr(self.video_player, 'duration'):
                if self.video_player.duration > 0:
                    current_time = self.video_player.get_current_timestamp()
                    total_time = self.video_player.duration
                    time_text = f"{self.format_time(current_time)} / {self.format_time(total_time)}"
                    self.time_info_label.config(text=time_text)
        
        except Exception:
            pass  # 忽略更新过程中的错误
        
        # 每100毫秒更新一次
        self.root.after(100, self.update_info)
    
    def format_time(self, seconds):
        """
        格式化时间显示。
        
        Args:
            seconds (float): 秒数
            
        Returns:
            str: 格式化的时间字符串
        """
        if seconds < 0:
            return "00:00"
        
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"
    
    def run(self):
        """
        运行演示应用程序。
        """
        # 显示使用说明
        messagebox.showinfo(
            "使用说明",
            "欢迎使用视频播放器组件演示!\n\n"
            "使用步骤:\n"
            "1. 点击'加载视频'按钮选择视频文件\n"
            "2. 在视频画面上拖拽鼠标选择字幕区域\n"
            "3. 拖动进度条浏览视频内容\n"
            "4. 使用'提取当前帧'或'批量提取'功能\n\n"
            "支持的视频格式: MP4, AVI, MKV, MOV, WMV"
        )
        
        # 启动主循环
        self.root.mainloop()


def main():
    """
    主函数。
    """
    try:
        # 创建并运行演示应用
        demo = VideoPlayerDemo()
        demo.run()
    
    except Exception as e:
        print(f"演示程序启动失败: {e}")
        messagebox.showerror("错误", f"演示程序启动失败:\n{str(e)}")


if __name__ == "__main__":
    main()