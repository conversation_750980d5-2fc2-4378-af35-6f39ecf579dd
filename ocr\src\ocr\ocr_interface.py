# -*- coding: utf-8 -*-
"""
定义OCR引擎的通用接口。
"""

from abc import ABC, abstractmethod
import numpy as np

class OcrInterface(ABC):
    """
    所有OCR引擎实现的抽象基类 (ABC)。
    """

    @abstractmethod
    def __init__(self, config: dict):
        """
        初始化OCR引擎。

        Args:
            config (dict): 特定于OCR引擎的配置字典。
        """
        pass

    @abstractmethod
    def recognize_text(self, image: np.ndarray) -> str:
        """
        从给定的图像中识别文本。

        Args:
            image (np.ndarray): 需要进行文本识别的图像 (NumPy数组)。

        Returns:
            str: 从图像中识别出的文本。
        """
        pass