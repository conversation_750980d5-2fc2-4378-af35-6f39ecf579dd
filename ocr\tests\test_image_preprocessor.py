# -*- coding: utf-8 -*-
"""
图像预处理模块的单元测试
"""

import os
import shutil
import pytest
import cv2
import numpy as np
from src.image.image_preprocessor import ImagePreprocessor

# 定义测试用的目录和文件
TEST_DIR = os.path.join(os.path.dirname(__file__), 'test_image_processing')
TEST_IMAGE_FILE = os.path.join(TEST_DIR, 'test_image.png')

def create_dummy_image(filepath: str, width: int, height: int):
    """创建一个用于测试的虚拟图像文件"""
    # 创建一个带有噪点的彩色图像
    image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
    # 添加一些文字
    cv2.putText(image, "Test", (10, height // 2), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
    cv2.imwrite(filepath, image)

@pytest.fixture(scope="module")
def setup_image_test_environment():
    """在测试开始前创建测试目录和图像，在测试结束后清理"""
    # Setup
    if os.path.exists(TEST_DIR):
        shutil.rmtree(TEST_DIR)
    os.makedirs(TEST_DIR)
    create_dummy_image(TEST_IMAGE_FILE, 200, 100)
    
    yield # 测试将在此处运行
    
    # Teardown
    shutil.rmtree(TEST_DIR)

def test_image_preprocessor_init(setup_image_test_environment):
    """测试ImagePreprocessor的初始化"""
    preprocessor = ImagePreprocessor(TEST_IMAGE_FILE)
    assert preprocessor.image is not None

    with pytest.raises(IOError):
        ImagePreprocessor('nonexistent.png')

def test_crop(setup_image_test_environment):
    """测试图像裁剪"""
    preprocessor = ImagePreprocessor(TEST_IMAGE_FILE)
    coords = {'x': 10, 'y': 20, 'width': 50, 'height': 30}
    original_shape = preprocessor.image.shape
    preprocessor.crop(coords)
    cropped_shape = preprocessor.get_processed_image().shape
    assert cropped_shape[0] == coords['height']
    assert cropped_shape[1] == coords['width']
    assert cropped_shape[2] == original_shape[2]

def test_to_grayscale(setup_image_test_environment):
    """测试转换为灰度图"""
    preprocessor = ImagePreprocessor(TEST_IMAGE_FILE)
    preprocessor.to_grayscale()
    processed_image = preprocessor.get_processed_image()
    assert len(processed_image.shape) == 2

def test_binarize(setup_image_test_environment):
    """测试二值化"""
    preprocessor = ImagePreprocessor(TEST_IMAGE_FILE)
    preprocessor.binarize(threshold=150)
    processed_image = preprocessor.get_processed_image()
    assert len(processed_image.shape) == 2 # Binarize should also convert to gray if not already
    # 检查图像中的值是否只有0和255
    unique_values = np.unique(processed_image)
    assert np.all(np.isin(unique_values, [0, 255]))

def test_chaining_operations(setup_image_test_environment):
    """测试链式调用预处理操作"""
    preprocessor = ImagePreprocessor(TEST_IMAGE_FILE)
    coords = {'x': 0, 'y': 0, 'width': 100, 'height': 50}
    processed_image = (
        preprocessor.crop(coords)
        .to_grayscale()
        .binarize()
        .get_processed_image()
    )
    assert processed_image.shape == (50, 100)
    assert len(processed_image.shape) == 2