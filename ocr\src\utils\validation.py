# -*- coding: utf-8 -*-
"""
数据验证相关的工具函数
"""

import os
from typing import Dict

def validate_video_file(filepath: str) -> bool:
    """
    验证指定的路径是否是一个有效的、存在的文件。

    Args:
        filepath (str): 文件路径。

    Returns:
        bool: 如果文件存在且是一个文件，则返回True。
    """
    return os.path.isfile(filepath)

def validate_coordinates(coords: Dict[str, int]) -> bool:
    """
    验证坐标字典是否包含所有必需的键 (x, y, width, height)
    并且所有值都是非负整数。

    Args:
        coords (Dict[str, int]): 包含坐标信息的字典。

    Returns:
        bool: 如果坐标有效则返回True。
    """
    required_keys = {'x', 'y', 'width', 'height'}
    if not required_keys.issubset(coords.keys()):
        return False
    
    return all(isinstance(coords[key], int) and coords[key] >= 0 for key in required_keys)

def validate_timestamp(timestamp: float) -> bool:
    """
    验证时间戳是否是一个非负数。

    Args:
        timestamp (float): 时间戳。

    Returns:
        bool: 如果时间戳有效则返回True。
    """
    return isinstance(timestamp, (int, float)) and timestamp >= 0