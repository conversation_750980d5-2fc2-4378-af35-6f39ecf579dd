["tests/test_config_manager.py::test_get_config_with_missing_key", "tests/test_config_manager.py::test_get_ocr_config", "tests/test_config_manager.py::test_get_video_config", "tests/test_config_manager.py::test_load_config", "tests/test_config_manager.py::test_validate_config", "tests/test_file_utils.py::test_create_backup_file", "tests/test_file_utils.py::test_create_backup_for_nonexistent_file", "tests/test_file_utils.py::test_ensure_directory_exists", "tests/test_file_utils.py::test_get_file_extension", "tests/test_image_preprocessor.py::test_binarize", "tests/test_image_preprocessor.py::test_chaining_operations", "tests/test_image_preprocessor.py::test_crop", "tests/test_image_preprocessor.py::test_image_preprocessor_init", "tests/test_image_preprocessor.py::test_to_grayscale", "tests/test_srt_generator.py::test_format_time_for_srt", "tests/test_srt_generator.py::test_generate_srt_content", "tests/test_srt_generator.py::test_save_to_file", "tests/test_subtitle_item.py::test_subtitle_item_creation", "tests/test_subtitle_item.py::test_subtitle_item_invalid_times", "tests/test_subtitle_item.py::test_subtitle_sequence_creation_and_add", "tests/test_subtitle_item.py::test_subtitle_sequence_iteration", "tests/test_subtitle_item.py::test_subtitle_sequence_sorting", "tests/test_subtitle_processor.py::test_empty_sequence", "tests/test_subtitle_processor.py::test_merge_similar_subtitles", "tests/test_subtitle_processor.py::test_merge_with_no_matches", "tests/test_tesseract_ocr.py::test_recognize_text_with_tesseract", "tests/test_tesseract_ocr.py::test_tesseract_not_found_error", "tests/test_validation.py::test_validate_coordinates", "tests/test_validation.py::test_validate_timestamp", "tests/test_validation.py::test_validate_video_file", "tests/test_video_player_widget.py::TestVideoPlayerWidget::test_clear_selection", "tests/test_video_player_widget.py::TestVideoPlayerWidget::test_coordinate_conversion", "tests/test_video_player_widget.py::TestVideoPlayerWidget::test_get_current_timestamp", "tests/test_video_player_widget.py::TestVideoPlayerWidget::test_get_subtitle_area", "tests/test_video_player_widget.py::TestVideoPlayerWidget::test_init_custom_config", "tests/test_video_player_widget.py::TestVideoPlayerWidget::test_init_default_config", "tests/test_video_player_widget.py::TestVideoPlayerWidget::test_load_config_from_yaml", "tests/test_video_player_widget.py::TestVideoPlayerWidget::test_load_video_file_not_exists", "tests/test_video_player_widget.py::TestVideoPlayerWidget::test_load_video_success", "tests/test_video_player_widget.py::TestVideoPlayerWidget::test_load_video_unsupported_format", "tests/test_video_player_widget.py::TestVideoPlayerWidget::test_set_position", "tests/test_video_processor.py::test_extract_and_crop_frame_invalid_crop", "tests/test_video_processor.py::test_extract_and_crop_frame_invalid_path", "tests/test_video_processor.py::test_extract_and_crop_frame_success", "tests/test_video_processor.py::test_extract_and_crop_frame_timestamp_out_of_range", "tests/test_video_processor.py::test_extract_frames", "tests/test_video_processor.py::test_get_frame_rate", "tests/test_video_processor.py::test_video_processor_init"]