# -*- coding: utf-8 -*-
"""
文件操作相关的工具函数
"""

import os
import shutil
from pathlib import Path

def ensure_directory_exists(path: str) -> bool:
    """
    确保指定的目录存在，如果不存在则创建。

    Args:
        path (str): 目录的路径。

    Returns:
        bool: 如果目录已存在或成功创建，则返回True。
    """
    try:
        Path(path).mkdir(parents=True, exist_ok=True)
        return True
    except OSError:
        # 可以添加日志记录来捕获具体的错误
        return False

def get_file_extension(filepath: str) -> str:
    """
    获取文件的小写扩展名。

    Args:
        filepath (str): 文件路径。

    Returns:
        str: 文件扩展名（不含点）。
    """
    return Path(filepath).suffix.lstrip('.').lower()

def create_backup_file(filepath: str) -> str:
    """
    为指定文件创建一个备份，备份文件名格式为 '原文件名.bak'。

    Args:
        filepath (str): 需要备份的文件路径。

    Returns:
        str: 备份文件的路径。如果备份失败，则返回空字符串。
    """
    if not os.path.exists(filepath):
        return ""
    backup_path = f"{filepath}.bak"
    try:
        shutil.copy2(filepath, backup_path)
        return backup_path
    except (IOError, OSError):
        return ""