# -*- coding: utf-8 -*-
"""
视频处理模块的单元测试
"""

import os
import shutil
import pytest
import cv2
import numpy as np
from src.video.video_processor import VideoProcessor

# 定义测试用的目录和文件
TEST_DIR = os.path.join(os.path.dirname(__file__), 'test_video_processing')
TEST_VIDEO_FILE = os.path.join(TEST_DIR, 'test_video.mp4')
OUTPUT_DIR = os.path.join(TEST_DIR, 'frames')

def create_dummy_video(filepath: str, width: int, height: int, duration_s: int, fps: int):
    """创建一个用于测试的虚拟视频文件"""
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(filepath, fourcc, fps, (width, height))
    if not out.isOpened():
        raise IOError(f"无法创建视频文件: {filepath}")

    for i in range(duration_s * fps):
        # 创建一个带有文本的黑色帧
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        text = f"Frame {i+1}"
        cv2.putText(frame, text, (50, height // 2), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        out.write(frame)
    out.release()

@pytest.fixture(scope="module")
def setup_video_test_environment():
    """在测试开始前创建测试目录和视频，在测试结束后清理"""
    # Setup
    if os.path.exists(TEST_DIR):
        shutil.rmtree(TEST_DIR)
    os.makedirs(TEST_DIR)
    create_dummy_video(TEST_VIDEO_FILE, 640, 480, 2, 30) # 2秒，30fps
    
    yield # 测试将在此处运行
    
    # Teardown
    shutil.rmtree(TEST_DIR)

def test_video_processor_init(setup_video_test_environment):
    """测试VideoProcessor的初始化"""
    processor = VideoProcessor(TEST_VIDEO_FILE)
    assert processor.cap.isOpened()
    del processor # 确保 __del__ 被调用

    with pytest.raises(FileNotFoundError):
        VideoProcessor('nonexistent.mp4')

def test_get_frame_rate(setup_video_test_environment):
    """测试获取视频帧率"""
    processor = VideoProcessor(TEST_VIDEO_FILE)
    assert processor.get_frame_rate() == 30.0

def test_extract_frames(setup_video_test_environment):
    """测试提取帧的功能"""
    processor = VideoProcessor(TEST_VIDEO_FILE)
    
    # 每500ms提取一帧
    frame_paths = processor.extract_frames(OUTPUT_DIR, interval_ms=500)
    
    # 2秒的视频，每500ms一帧，应该有 2s / 0.5s = 4 帧
    # 确切的帧数可能因时间戳计算而略有不同，我们检查一个合理的范围
    assert 4 <= len(frame_paths) <= 5
    
    for path in frame_paths:
        assert os.path.exists(path)
        # 检查文件名是否符合格式 'frame_TIMESTAMP.png'
        assert path.startswith(os.path.join(OUTPUT_DIR, 'frame_'))
        assert path.endswith('.png')