import pytest
import os
import cv2
from src.video.video_processor import extract_and_crop_frame

# Define a fixture for the test video path
@pytest.fixture
def video_path():
    """
    Provides the path to the test video.
    Note: Please place a test video file named 'test_video.mp4'
    in the 'video/' directory at the project root.
    """
    path = "video/test_video.mp4"
    if not os.path.exists(path):
        pytest.skip("Test video not found. Please place 'test_video.mp4' in the 'video/' directory.")
    return path

# Define a fixture for the output directory
@pytest.fixture
def output_dir(tmpdir):
    """
    Creates a temporary directory for test output.
    """
    return str(tmpdir)

def test_extract_and_crop_frame_success(video_path, output_dir):
    """
    测试 extract_and_crop_frame 函数是否成功提取、裁剪并保存帧。
    """
    timestamp = 1.0  # 提取第1秒的帧
    crop_coordinates = {'x': 100, 'y': 100, 'width': 200, 'height': 150}
    
    # 调用函数
    result_path = extract_and_crop_frame(
        video_path=video_path,
        timestamp=timestamp,
        crop_coordinates=crop_coordinates,
        output_dir=output_dir
    )
    
    # 验证返回值
    assert result_path is not None
    assert os.path.exists(result_path)
    
    # 验证文件名
    expected_filename = f"frame_{timestamp}s.png"
    assert os.path.basename(result_path) == expected_filename
    
    # 验证图像尺寸
    img = cv2.imread(result_path)
    assert img is not None
    assert img.shape == (crop_coordinates['height'], crop_coordinates['width'], 3)

def test_extract_and_crop_frame_invalid_path():
    """
    测试当视频路径无效时函数是否返回 None。
    """
    result = extract_and_crop_frame(
        video_path="non_existent_video.mp4",
        timestamp=1.0,
        crop_coordinates={'x': 0, 'y': 0, 'width': 10, 'height': 10},
        output_dir="."
    )
    assert result is None

def test_extract_and_crop_frame_timestamp_out_of_range(video_path, output_dir):
    """
    测试当时间戳超出范围时函数是否返回 None。
    """
    result = extract_and_crop_frame(
        video_path=video_path,
        timestamp=99999.0,  # 一个肯定超出范围的时间戳
        crop_coordinates={'x': 0, 'y': 0, 'width': 10, 'height': 10},
        output_dir=output_dir
    )
    assert result is None

def test_extract_and_crop_frame_invalid_crop(video_path, output_dir):
    """
    测试当裁剪坐标无效时函数是否返回 None。
    """
    result = extract_and_crop_frame(
        video_path=video_path,
        timestamp=1.0,
        crop_coordinates={'x': 9999, 'y': 9999, 'width': 100, 'height': 100}, # 无效坐标
        output_dir=output_dir
    )
    assert result is None