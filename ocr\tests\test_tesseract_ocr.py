# -*- coding: utf-8 -*-
"""
Tesseract OCR实现的单元测试
"""

import os
import pytest
import numpy as np
import cv2
import pytesseract
from src.ocr.tesseract_ocr import TesseractOcr

# 检查Tesseract是否可用，如果不可用则跳过测试
try:
    pytesseract.get_tesseract_version()
    TESSERACT_AVAILABLE = True
except pytesseract.TesseractNotFoundError:
    TESSERACT_AVAILABLE = False

@pytest.mark.skipif(not TESSERACT_AVAILABLE, reason="Tesseract is not installed or not in PATH")
def test_recognize_text_with_tesseract():
    """测试使用Tesseract进行文本识别"""
    # 创建一个带有清晰文本的测试图像
    width, height = 400, 100
    image = np.zeros((height, width, 3), dtype=np.uint8)
    image.fill(255) # 白色背景
    text = "Hello, Tesseract!"
    cv2.putText(image, text, (20, 60), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 0), 2, cv2.LINE_AA)

    # 初始化Tesseract OCR
    # 在CI/CD环境中，可能需要指定tesseract_cmd
    config = {'lang': 'eng'}
    ocr_engine = TesseractOcr(config)

    # 识别文本
    recognized_text = ocr_engine.recognize_text(image)

    # Tesseract的识别结果可能不完美，我们检查关键部分
    assert "Hello" in recognized_text
    assert "Tesseract" in recognized_text

def test_tesseract_not_found_error():
    """测试当Tesseract未安装时是否引发正确的异常"""
    # 强制一个无效的Tesseract命令路径
    config = {'tesseract_cmd': '/invalid/path/to/tesseract'}
    ocr_engine = TesseractOcr(config)
    
    # 创建一个虚拟图像
    image = np.zeros((10, 10, 3), dtype=np.uint8)

    with pytest.raises(RuntimeError, match="Tesseract is not installed or not in your PATH"):
        ocr_engine.recognize_text(image)