# -*- coding: utf-8 -*-
"""
配置管理模块的单元测试
"""

import os
import pytest
from src.config.config_manager import (
    load_config,
    validate_config,
    get_video_config,
    get_ocr_config
)

# 定义测试配置文件的路径
TEST_CONFIG_PATH = os.path.join(os.path.dirname(__file__), 'test_config.yaml')

@pytest.fixture
def config_data():
    """提供测试用的配置数据"""
    return load_config(TEST_CONFIG_PATH)

def test_load_config():
    """测试是否能成功加载YAML配置文件"""
    config = load_config(TEST_CONFIG_PATH)
    assert isinstance(config, dict)
    assert 'video' in config
    assert 'ocr' in config

def test_validate_config(config_data):
    """测试配置验证功能（目前为占位符）"""
    assert validate_config(config_data) is True

def test_get_video_config(config_data):
    """测试获取视频配置的功能"""
    video_config = get_video_config(config_data)
    assert isinstance(video_config, dict)
    assert video_config['frame_interval'] == 1.5
    assert video_config['crop_area']['x'] == 10

def test_get_ocr_config(config_data):
    """测试获取OCR配置的功能"""
    ocr_config = get_ocr_config(config_data)
    assert isinstance(ocr_config, dict)
    assert ocr_config['provider'] == 'test_provider'
    assert ocr_config['language'] == 'eng'

def test_get_config_with_missing_key():
    """测试当配置中缺少某个键时，函数能返回默认空字典"""
    config = {'other_key': 'value'}
    video_config = get_video_config(config)
    ocr_config = get_ocr_config(config)
    assert video_config == {}
    assert ocr_config == {}