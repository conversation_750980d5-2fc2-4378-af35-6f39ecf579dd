# -*- coding: utf-8 -*-
"""
处理字幕序列，例如合并相似的字幕条目。
"""

from typing import List
from difflib import SequenceMatcher

from .subtitle_item import SubtitleItem, SubtitleSequence

class SubtitleProcessor:
    """
    一个用于处理和优化字幕序列的类。
    """

    def __init__(self, sequence: SubtitleSequence):
        """
        初始化SubtitleProcessor。

        Args:
            sequence (SubtitleSequence): 要处理的字幕序列。
        """
        self.sequence = sequence

    def merge_similar_subtitles(
        self, similarity_threshold: float = 0.9, max_time_gap: float = 0.5
    ) -> SubtitleSequence:
        """
        合并内容相似且时间上连续的字幕条目。

        Args:
            similarity_threshold (float): 用于判断文本是否相似的阈值 (0到1之间)。
            max_time_gap (float): 相邻字幕之间允许的最大时间间隔（秒）。

        Returns:
            SubtitleSequence: 包含合并后字幕的新序列。
        """
        if not self.sequence.items:
            return SubtitleSequence()

        # 确保字幕按时间排序
        self.sequence.sort_by_time()

        merged_items: List[SubtitleItem] = []
        current_item = self.sequence.items[0]

        for i in range(1, len(self.sequence.items)):
            next_item = self.sequence.items[i]
            
            time_gap = next_item.start_time - current_item.end_time
            text_similarity = SequenceMatcher(None, current_item.text, next_item.text).ratio()

            if time_gap <= max_time_gap and text_similarity >= similarity_threshold:
                # 合并字幕：延长结束时间
                current_item.end_time = next_item.end_time
            else:
                # 不合并，将当前字幕添加到列表并开始新的合并段
                merged_items.append(current_item)
                current_item = next_item
        
        # 添加最后一个字幕条目
        merged_items.append(current_item)

        return SubtitleSequence(items=merged_items)