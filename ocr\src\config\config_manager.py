# -*- coding: utf-8 -*-
"""
配置管理模块

负责加载、验证和提供配置参数。
"""

import yaml
from typing import Dict, Any

# 使用类型别名以增强可读性
ConfigDict = Dict[str, Any]

def load_config(config_path: str) -> ConfigDict:
    """
    从指定的YAML文件加载配置。

    Args:
        config_path (str): YAML配置文件的路径。

    Returns:
        ConfigDict: 包含配置信息的字典。
    """
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def validate_config(config: ConfigDict) -> bool:
    """
    验证配置字典的完整性和正确性。
    (目前为占位符)

    Args:
        config (ConfigDict): 待验证的配置字典。

    Returns:
        bool: 如果配置有效则返回True，否则返回False。
    """
    # TODO: 实现详细的配置验证逻辑
    return True

def get_video_config(config: ConfigDict) -> Dict[str, Any]:
    """
    从主配置中获取视频处理相关的配置。

    Args:
        config (ConfigDict): 完整的配置字典。

    Returns:
        Dict[str, Any]: 视频配置。
    """
    return config.get('video', {})

def get_ocr_config(config: ConfigDict) -> Dict[str, Any]:
    """
    从主配置中获取OCR相关的配置。

    Args:
        config (ConfigDict): 完整的配置字典。

    Returns:
        Dict[str, Any]: OCR配置。
    """
    return config.get('ocr', {})

def get_default_config() -> ConfigDict:
    """
    返回一个默认的配置字典。

    Returns:
        ConfigDict: 默认配置。
    """
    return {
        'video': {
            'path': 'path/to/your/video.mp4',
            'subtitle_area': {'x1': 0, 'y1': 700, 'x2': 1920, 'y2': 1000},
            'process_interval': 1.0
        },
        'image': {
            'binary_threshold': 180
        },
        'ocr': {
            'tesseract_cmd': 'tesseract',
            'lang': 'chi_sim+eng'
        },
        'subtitle': {
            'similarity_threshold': 0.8,
            'merge_time_threshold': 1.0
        },
        'output': {
            'directory': 'output'
        }
    }

# 可以根据需要添加更多获取特定配置的函数
# 例如: get_image_config, get_subtitle_config 等