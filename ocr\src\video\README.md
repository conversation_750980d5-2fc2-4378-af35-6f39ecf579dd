# 视频播放器组件 (VideoPlayerWidget)

专门用于字幕区域选择和预览的Python视频播放器GUI组件。

## 功能特性

### 🎥 视频播放功能
- **多格式支持**: MP4, AVI, MKV, MOV, WMV
- **精确控制**: 支持帧级别的精确定位
- **实时预览**: 拖动进度条时实时更新画面
- **自适应显示**: 自动按比例缩放视频画面

### 🎯 字幕区域选择
- **鼠标拖拽**: 直观的矩形选择框绘制
- **多方向支持**: 支持8个方向的拖拽操作
- **坐标转换**: 自动转换显示坐标到原始视频坐标
- **实时反馈**: 选择过程中的视觉反馈

### ⚡ 性能优化
- **帧缓存机制**: 避免重复解码提升性能
- **异步处理**: 界面更新不阻塞主线程
- **内存管理**: 智能的资源释放机制

### 🔧 模块化设计
- **独立组件**: 易于集成到现有项目
- **配置灵活**: 支持YAML配置文件
- **API清晰**: 简洁明了的接口设计

## 文件结构

```
src/video/
├── video_player_widget.py    # 主要的视频播放器组件
├── video_player_example.py   # 集成使用示例
├── video_processor.py        # 视频处理功能
└── README.md                 # 本文档

tests/
├── test_video_player_widget.py  # 组件单元测试
└── test_video_processor.py      # 处理器测试

demo_video_player.py          # 完整演示应用
```

## 快速开始

### 1. 环境要求

```bash
# 安装依赖
pip install opencv-python
pip install pillow
pip install pyyaml

# Python版本要求
Python >= 3.7
```

### 2. 基本使用

```python
import tkinter as tk
from src.video.video_player_widget import VideoPlayerWidget

# 创建主窗口
root = tk.Tk()
root.title("视频播放器")

# 创建视频播放器组件
player = VideoPlayerWidget(parent=root)
player.pack(fill=tk.BOTH, expand=True)

# 加载视频
success = player.load_video("path/to/your/video.mp4")
if success:
    print("视频加载成功")

# 启动GUI
root.mainloop()
```

### 3. 运行演示程序

```bash
# 运行完整演示
python demo_video_player.py

# 运行集成示例
python src/video/video_player_example.py
```

## API 参考

### VideoPlayerWidget 类

#### 初始化

```python
VideoPlayerWidget(parent=None, config=None)
```

**参数:**
- `parent`: 父容器组件
- `config`: 配置字典，可选

**默认配置:**
```python
{
    'window_width': 800,
    'window_height': 600,
    'canvas_bg': 'black',
    'selection_color': 'red',
    'selection_width': 2,
    'supported_formats': ['.mp4', '.avi', '.mkv', '.mov', '.wmv']
}
```

#### 主要方法

##### load_video(video_path: str) -> bool
加载视频文件。

**参数:**
- `video_path`: 视频文件路径

**返回:**
- `bool`: 加载是否成功

**示例:**
```python
success = player.load_video("video.mp4")
if success:
    print(f"视频尺寸: {player.video_width}x{player.video_height}")
    print(f"视频时长: {player.duration:.2f}秒")
```

##### get_subtitle_area() -> tuple | None
获取用户选择的字幕区域坐标。

**返回:**
- `tuple`: (x, y, width, height) 基于原始视频分辨率
- `None`: 未选择区域

**示例:**
```python
area = player.get_subtitle_area()
if area:
    x, y, width, height = area
    print(f"选择区域: ({x}, {y}) - {width}x{height}")
```

##### set_position(timestamp: float) -> bool
设置播放位置到指定时间戳。

**参数:**
- `timestamp`: 时间戳（秒）

**返回:**
- `bool`: 设置是否成功

**示例:**
```python
# 跳转到第30秒
success = player.set_position(30.0)
```

##### get_current_frame() -> np.ndarray | None
获取当前显示的视频帧。

**返回:**
- `np.ndarray`: 当前帧的图像数据
- `None`: 无可用帧

##### get_current_timestamp() -> float
获取当前播放位置的时间戳。

**返回:**
- `float`: 当前时间戳（秒）

## 配置文件

支持通过YAML文件进行配置：

```yaml
# config.yaml
video_player:
  window_width: 1000
  window_height: 700
  canvas_bg: "#000000"
  selection_color: "#ff0000"
  selection_width: 3
  supported_formats:
    - ".mp4"
    - ".avi"
    - ".mkv"
    - ".mov"
    - ".wmv"
```

## 集成指南

### 与 video_processor.py 集成

```python
from src.video.video_player_widget import VideoPlayerWidget
from src.video.video_processor import extract_and_crop_frame

# 获取选择区域
selection_area = player.get_subtitle_area()
current_time = player.get_current_timestamp()

# 提取并裁剪帧
if selection_area:
    output_path = extract_and_crop_frame(
        video_path="video.mp4",
        timestamp=current_time,
        crop_coordinates=selection_area,
        output_dir="./output",
        filename_prefix="subtitle"
    )
```

### 自定义事件处理

```python
class CustomVideoPlayer(VideoPlayerWidget):
    def __init__(self, parent=None, config=None):
        super().__init__(parent, config)
        
        # 绑定自定义事件
        self.canvas.bind('<Double-Button-1>', self.on_double_click)
    
    def on_double_click(self, event):
        """双击事件处理"""
        print("双击位置:", event.x, event.y)
    
    def on_selection_changed(self, area):
        """选择区域改变时的回调"""
        if area:
            print(f"新选择区域: {area}")
```

## 测试

### 运行单元测试

```bash
# 运行所有测试
pytest tests/ -v

# 运行特定测试
pytest tests/test_video_player_widget.py -v

# 运行带覆盖率的测试
pytest tests/ --cov=src/video --cov-report=html
```

### 测试用例说明

- **test_init_default_config**: 测试默认配置初始化
- **test_load_video_success**: 测试成功加载视频
- **test_coordinate_conversion**: 测试坐标转换功能
- **test_set_position**: 测试位置设置功能
- **test_get_subtitle_area**: 测试区域获取功能

## 故障排除

### 常见问题

#### 1. 视频加载失败

**问题**: `load_video()` 返回 `False`

**解决方案**:
- 检查文件路径是否正确
- 确认视频格式是否支持
- 检查文件是否损坏
- 确认OpenCV安装正确

```python
# 调试代码
import cv2
cap = cv2.VideoCapture("your_video.mp4")
print(f"视频可打开: {cap.isOpened()}")
cap.release()
```

#### 2. 坐标转换不准确

**问题**: 选择区域坐标与预期不符

**解决方案**:
- 确保视频已正确加载
- 检查显示缩放比例计算
- 验证鼠标事件坐标

```python
# 调试坐标转换
print(f"原始尺寸: {player.video_width}x{player.video_height}")
print(f"显示尺寸: {player.display_width}x{player.display_height}")
print(f"缩放比例: {player.scale_factor_x}, {player.scale_factor_y}")
```

#### 3. 界面卡顿

**问题**: 拖动进度条时界面响应慢

**解决方案**:
- 减少帧缓存大小
- 使用较低的更新频率
- 检查视频文件大小和编码

#### 4. 内存占用过高

**问题**: 长时间使用后内存持续增长

**解决方案**:
- 定期清理帧缓存
- 确保正确释放OpenCV资源
- 检查事件绑定是否正确解除

### 调试模式

启用调试输出：

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 创建播放器时启用调试
config = {'debug': True}
player = VideoPlayerWidget(parent=root, config=config)
```

## 性能优化建议

### 1. 视频文件优化
- 使用H.264编码的MP4文件
- 避免过高的分辨率（建议1080p以下）
- 使用合适的比特率

### 2. 内存优化
- 限制帧缓存大小
- 及时释放不需要的资源
- 使用较小的显示尺寸

### 3. 响应性优化
- 使用线程处理耗时操作
- 减少不必要的界面更新
- 优化事件处理逻辑

## 扩展功能

### 1. 添加播放控制

```python
class ExtendedVideoPlayer(VideoPlayerWidget):
    def __init__(self, parent=None, config=None):
        super().__init__(parent, config)
        self.is_playing = False
        self.play_timer = None
    
    def play(self):
        """开始播放"""
        if not self.is_playing:
            self.is_playing = True
            self._play_loop()
    
    def pause(self):
        """暂停播放"""
        self.is_playing = False
        if self.play_timer:
            self.root.after_cancel(self.play_timer)
    
    def _play_loop(self):
        """播放循环"""
        if self.is_playing and self.video_capture:
            # 更新到下一帧
            next_frame = self.current_frame_number + 1
            if next_frame < self.total_frames:
                self._show_frame(next_frame)
                # 根据帧率计算延迟
                delay = int(1000 / self.fps)
                self.play_timer = self.root.after(delay, self._play_loop)
            else:
                self.is_playing = False
```

### 2. 添加多区域选择

```python
class MultiSelectionVideoPlayer(VideoPlayerWidget):
    def __init__(self, parent=None, config=None):
        super().__init__(parent, config)
        self.selection_areas = []  # 存储多个选择区域
    
    def add_selection_area(self, area):
        """添加选择区域"""
        self.selection_areas.append(area)
    
    def get_all_selection_areas(self):
        """获取所有选择区域"""
        return self.selection_areas.copy()
    
    def clear_all_selections(self):
        """清除所有选择"""
        self.selection_areas.clear()
        self._redraw_canvas()
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 初始版本发布
- 🎥 基础视频播放功能
- 🎯 字幕区域选择功能
- 📝 完整的文档和示例
- 🧪 单元测试覆盖

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

---

**感谢使用视频播放器组件！** 🎉