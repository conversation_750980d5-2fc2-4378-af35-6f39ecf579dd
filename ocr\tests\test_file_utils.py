# -*- coding: utf-8 -*-
"""
文件工具模块的单元测试
"""

import os
import shutil
import pytest
from src.utils.file_utils import (
    ensure_directory_exists,
    get_file_extension,
    create_backup_file
)

# 定义测试用的目录和文件
TEST_DIR = os.path.join(os.path.dirname(__file__), 'test_dir_for_utils')
TEST_FILE = os.path.join(TEST_DIR, 'test_file.txt')

@pytest.fixture(scope="module")
def setup_test_environment():
    """在测试开始前创建测试目录和文件，在测试结束后清理"""
    # Setup
    if os.path.exists(TEST_DIR):
        shutil.rmtree(TEST_DIR)
    os.makedirs(TEST_DIR)
    with open(TEST_FILE, 'w') as f:
        f.write('test content')
    
    yield # 测试将在此处运行
    
    # Teardown
    shutil.rmtree(TEST_DIR)

def test_ensure_directory_exists(setup_test_environment):
    """测试确保目录存在的功能"""
    new_dir_path = os.path.join(TEST_DIR, 'new_dir')
    assert not os.path.exists(new_dir_path)
    result = ensure_directory_exists(new_dir_path)
    assert result is True
    assert os.path.isdir(new_dir_path)

def test_get_file_extension():
    """测试获取文件扩展名的功能"""
    assert get_file_extension('video.MP4') == 'mp4'
    assert get_file_extension('archive.tar.gz') == 'gz'
    assert get_file_extension('no_extension') == ''

def test_create_backup_file(setup_test_environment):
    """测试创建备份文件的功能"""
    backup_path = create_backup_file(TEST_FILE)
    assert backup_path == f"{TEST_FILE}.bak"
    assert os.path.exists(backup_path)
    with open(backup_path, 'r') as f:
        content = f.read()
    assert content == 'test content'

def test_create_backup_for_nonexistent_file():
    """测试为不存在的文件创建备份应失败"""
    result = create_backup_file('nonexistent/file.txt')
    assert result == ""