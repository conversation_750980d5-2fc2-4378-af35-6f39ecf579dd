# -*- coding: utf-8 -*-
"""
验证工具模块的单元测试
"""

import os
import pytest
from src.utils.validation import (
    validate_video_file,
    validate_coordinates,
    validate_timestamp
)

# 使用在 test_file_utils.py 中定义的测试环境
from .test_file_utils import setup_test_environment, TEST_FILE

def test_validate_video_file(setup_test_environment):
    """测试视频文件验证功能"""
    assert validate_video_file(TEST_FILE) is True
    assert validate_video_file('nonexistent/file.mp4') is False
    assert validate_video_file(os.path.dirname(TEST_FILE)) is False # Should fail for a directory

def test_validate_coordinates():
    """测试坐标验证功能"""
    good_coords = {'x': 0, 'y': 10, 'width': 100, 'height': 50}
    bad_coords_missing = {'x': 0, 'y': 10, 'width': 100}
    bad_coords_negative = {'x': 0, 'y': 10, 'width': -100, 'height': 50}
    bad_coords_type = {'x': 0, 'y': '10', 'width': 100, 'height': 50}
    
    assert validate_coordinates(good_coords) is True
    assert validate_coordinates(bad_coords_missing) is False
    assert validate_coordinates(bad_coords_negative) is False
    assert validate_coordinates(bad_coords_type) is False

def test_validate_timestamp():
    """测试时间戳验证功能"""
    assert validate_timestamp(10.5) is True
    assert validate_timestamp(0) is True
    assert validate_timestamp(-1.0) is False
    assert validate_timestamp('not_a_float') is False