# 视频硬字幕提取转换系统 - 默认配置文件

# 视频处理配置
video:
  frame_interval: 1.0  # 帧提取间隔（秒）
  crop_area:           # 字幕区域坐标
    x: 0               # 左上角X坐标
    y: 720             # 左上角Y坐标（假设1080p视频）
    width: 1920        # 裁剪宽度
    height: 200        # 裁剪高度
  supported_formats:   # 支持的视频格式
    - "mp4"
    - "avi"
    - "mkv"
    - "mov"
    - "wmv"

# 图像处理配置
image_processing:
  grayscale: true                # 是否转换为灰度图
  binary_threshold: 127          # 二值化阈值 (0-255)
  denoise: true                  # 是否进行降噪处理
  contrast_enhancement: 1.2      # 对比度增强倍数
  resize_factor: 1.0             # 图像缩放因子
  blur_kernel_size: 3            # 模糊核大小
  morphology_operations:         # 形态学操作
    enabled: true
    kernel_size: 2
    iterations: 1

# OCR配置
ocr:
  provider: "tesseract"          # OCR提供者: tesseract, paddleocr, easyocr
  language: "chi_sim"            # 识别语言
  confidence_threshold: 0.6      # 置信度阈值
  batch_size: 10                 # 批处理大小
  timeout: 30                    # 超时时间（秒）
  tesseract:                     # Tesseract特定配置
    psm: 6                       # 页面分割模式
    oem: 3                       # OCR引擎模式
    whitelist: ""                # 字符白名单
    blacklist: ""                # 字符黑名单
  paddleocr:                     # PaddleOCR特定配置
    use_angle_cls: true          # 是否使用角度分类
    use_gpu: false               # 是否使用GPU
    det_model_dir: ""            # 检测模型路径
    rec_model_dir: ""            # 识别模型路径

# 字幕处理配置
subtitle:
  min_duration: 0.5              # 最小字幕持续时间（秒）
  max_duration: 10.0             # 最大字幕持续时间（秒）
  duplicate_threshold: 0.8       # 重复检测相似度阈值
  text_cleaning: true            # 是否进行文本清理
  merge_consecutive: true        # 是否合并连续相同字幕
  min_text_length: 1             # 最小文本长度
  max_text_length: 200           # 最大文本长度
  filter_patterns:               # 过滤模式
    - "^\\s*$"                   # 空白行
    - "^[\\d\\s]*$"              # 纯数字行
  cleaning_rules:                # 文本清理规则
    remove_extra_spaces: true    # 移除多余空格
    remove_special_chars: false  # 移除特殊字符
    normalize_punctuation: true  # 标准化标点符号

# 输出配置
output:
  encoding: "utf-8"              # 文件编码
  format: "srt"                  # 输出格式
  line_ending: "\\n"             # 行结束符
  timestamp_format: "srt"        # 时间戳格式
  backup_original: false         # 是否备份原文件
  output_directory: "output"     # 输出目录
  filename_template: "{name}_subtitles.{ext}"  # 文件名模板

# 性能配置
performance:
  max_workers: 4                 # 最大工作线程数
  chunk_size: 100                # 处理块大小
  memory_limit_mb: 2048          # 内存限制（MB）
  enable_progress_bar: true      # 是否显示进度条
  cache_enabled: true            # 是否启用缓存
  cache_size_mb: 512             # 缓存大小（MB）

# 日志配置
logging:
  level: "INFO"                  # 日志级别: DEBUG, INFO, WARNING, ERROR
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  file_enabled: true             # 是否写入日志文件
  file_path: "logs/subtitle_extractor.log"
  file_rotation: "10 MB"         # 日志文件轮转大小
  file_retention: "7 days"       # 日志文件保留时间
  console_enabled: true          # 是否输出到控制台

# 调试配置
debug:
  save_intermediate_images: false  # 是否保存中间处理图像
  save_ocr_results: false         # 是否保存OCR原始结果
  intermediate_dir: "debug"       # 中间文件保存目录
  profile_performance: false      # 是否进行性能分析
  verbose_errors: true            # 是否显示详细错误信息
