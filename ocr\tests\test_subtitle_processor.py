# -*- coding: utf-8 -*-
"""
字幕处理模块的单元测试
"""

import pytest
from src.subtitle.subtitle_item import SubtitleItem, SubtitleSequence
from src.subtitle.subtitle_processor import SubtitleProcessor

@pytest.fixture
def sample_subtitle_sequence() -> SubtitleSequence:
    """创建一个用于测试的样本字幕序列"""
    seq = SubtitleSequence()
    seq.add_item(SubtitleItem(1.0, 2.0, "Hello world"))
    seq.add_item(SubtitleItem(2.1, 3.0, "Hello world")) # 相似文本，时间连续
    seq.add_item(SubtitleItem(4.0, 5.0, "This is a test")) # 时间不连续
    seq.add_item(SubtitleItem(5.1, 6.0, "This is a test")) # 相似文本，时间连续
    seq.add_item(SubtitleItem(6.2, 7.0, "Different text")) # 文本不相似
    seq.add_item(SubtitleItem(8.0, 9.0, "Final line"))
    return seq

def test_merge_similar_subtitles(sample_subtitle_sequence):
    """测试合并相似字幕的功能"""
    processor = SubtitleProcessor(sample_subtitle_sequence)
    merged_sequence = processor.merge_similar_subtitles(
        similarity_threshold=0.8, max_time_gap=0.2
    )

    assert len(merged_sequence) == 4

    # 检查第一个合并的字幕
    assert merged_sequence.items[0].start_time == 1.0
    assert merged_sequence.items[0].end_time == 3.0
    assert merged_sequence.items[0].text == "Hello world"

    # 检查第二个合并的字幕
    assert merged_sequence.items[1].start_time == 4.0
    assert merged_sequence.items[1].end_time == 6.0
    assert merged_sequence.items[1].text == "This is a test"

    # 检查未合并的字幕
    assert merged_sequence.items[2].text == "Different text"
    assert merged_sequence.items[3].text == "Final line"

def test_merge_with_no_matches():
    """测试当没有可合并的字幕时的行为"""
    seq = SubtitleSequence()
    seq.add_item(SubtitleItem(1, 2, "A"))
    seq.add_item(SubtitleItem(3, 4, "B")) # 时间间隔太大
    seq.add_item(SubtitleItem(5, 6, "C"))

    processor = SubtitleProcessor(seq)
    merged_sequence = processor.merge_similar_subtitles()

    assert len(merged_sequence) == 3
    assert [item.text for item in merged_sequence.items] == ["A", "B", "C"]

def test_empty_sequence():
    """测试处理空序列时的行为"""
    processor = SubtitleProcessor(SubtitleSequence())
    merged_sequence = processor.merge_similar_subtitles()
    assert len(merged_sequence) == 0