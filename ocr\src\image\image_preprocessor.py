# -*- coding: utf-8 -*-
"""
图像预处理模块，用于增强图像质量以便于OCR识别。
"""

import cv2
import numpy as np
from typing import Dict, Optional

class ImagePreprocessor:
    """
    一个用于预处理图像以进行OCR的类。
    """

    def __init__(self, image_path: str):
        """
        初始化ImagePreprocessor。

        Args:
            image_path (str): 图像文件的路径。
        """
        self.image = cv2.imread(image_path)
        if self.image is None:
            raise IOError(f"无法加载图像: {image_path}")

    def crop(self, coords: Dict[str, int]) -> 'ImagePreprocessor':
        """
        根据提供的坐标裁剪图像。

        Args:
            coords (Dict[str, int]): 包含 'x', 'y', 'width', 'height' 的字典。

        Returns:
            self: 返回预处理器实例以支持链式调用。
        """
        x, y, w, h = coords['x'], coords['y'], coords['width'], coords['height']
        self.image = self.image[y:y+h, x:x+w]
        return self

    def to_grayscale(self) -> 'ImagePreprocessor':
        """
        将图像转换为灰度图。

        Returns:
            self: 返回预处理器实例以支持链式调用。
        """
        self.image = cv2.cvtColor(self.image, cv2.COLOR_BGR2GRAY)
        return self

    def binarize(self, threshold: int = 128) -> 'ImagePreprocessor':
        """
        对灰度图像进行二值化处理。

        Args:
            threshold (int): 二值化的阈值。

        Returns:
            self: 返回预处理器实例以支持链式调用。
        """
        if len(self.image.shape) == 3: # 如果不是灰度图，先转换
            self.to_grayscale()
        _, self.image = cv2.threshold(self.image, threshold, 255, cv2.THRESH_BINARY)
        return self

    def denoise(self) -> 'ImagePreprocessor':
        """
        对图像进行降噪处理。

        Returns:
            self: 返回预处理器实例以支持链式调用。
        """
        self.image = cv2.fastNlMeansDenoising(self.image, None, 10, 7, 21)
        return self

    def enhance_contrast(self, factor: float = 1.5) -> 'ImagePreprocessor':
        """
        增强图像的对比度。

        Args:
            factor (float): 对比度增强因子。

        Returns:
            self: 返回预处理器实例以支持链式调用。
        """
        if len(self.image.shape) == 2:
            # 对于灰度图
            self.image = cv2.convertScaleAbs(self.image, alpha=factor, beta=0)
        else:
            # 对于彩色图，在YUV空间中处理亮度通道
            img_yuv = cv2.cvtColor(self.image, cv2.COLOR_BGR2YUV)
            img_yuv[:,:,0] = cv2.equalizeHist(img_yuv[:,:,0])
            self.image = cv2.cvtColor(img_yuv, cv2.COLOR_YUV2BGR)
        return self

    def resize(self, factor: float) -> 'ImagePreprocessor':
        """
        调整图像大小。

        Args:
            factor (float): 缩放因子。

        Returns:
            self: 返回预处理器实例以支持链式调用。
        """
        self.image = cv2.resize(self.image, None, fx=factor, fy=factor, interpolation=cv2.INTER_CUBIC)
        return self

    def blur(self, kernel_size: int = 3) -> 'ImagePreprocessor':
        """
        对图像进行高斯模糊。

        Args:
            kernel_size (int): 模糊核的大小（必须是正奇数）。

        Returns:
            self: 返回预处理器实例以支持链式调用。
        """
        if kernel_size > 0 and kernel_size % 2 == 1:
            self.image = cv2.GaussianBlur(self.image, (kernel_size, kernel_size), 0)
        return self

    def apply_morphology(self, kernel_size: int = 2, iterations: int = 1) -> 'ImagePreprocessor':
        """
        应用形态学操作（开运算）来移除小的噪声点。

        Args:
            kernel_size (int): 核的大小。
            iterations (int): 迭代次数。

        Returns:
            self: 返回预处理器实例以支持链式调用。
        """
        kernel = np.ones((kernel_size, kernel_size), np.uint8)
        self.image = cv2.morphologyEx(self.image, cv2.MORPH_OPEN, kernel, iterations=iterations)
        return self

    def get_image(self) -> np.ndarray:
        """
        获取处理后的图像数据。

        Returns:
            np.ndarray: 处理后的图像（NumPy数组）。
        """
        return self.image