# -*- coding: utf-8 -*-
"""
使用Tesseract OCR引擎的实现。
"""

import pytesseract
import numpy as np
from PIL import Image
import cv2

from .ocr_interface import OcrInterface

class TesseractOcr(OcrInterface):
    """
    一个使用Pytesseract库的OCR引擎实现。
    """

    def __init__(self, config: dict):
        """
        初始化Tesseract OCR引擎。

        Args:
            config (dict): Tesseract的配置字典。
                           可以包含 'tesseract_cmd' (Tesseract可执行文件的路径)
                           和 'lang' (识别语言)。
        """
        self.config = config
        tesseract_cmd = self.config.get('tesseract_cmd')
        if tesseract_cmd:
            pytesseract.pytesseract.tesseract_cmd = tesseract_cmd

    def recognize_text(self, image: np.ndarray) -> str:
        """
        使用Tesseract从图像中识别文本。

        Args:
            image (np.ndarray): 需要进行文本识别的图像 (NumPy数组)。

        Returns:
            str: 从图像中识别出的文本。
        """
        # Pytesseract需要PIL图像
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB) if len(image.shape) == 3 else image)
        
        lang = self.config.get('lang', 'eng')
        custom_config = self.config.get('custom_config', r'--oem 3 --psm 6')

        try:
            text = pytesseract.image_to_string(
                pil_image, 
                lang=lang, 
                config=custom_config
            )
            return text.strip()
        except pytesseract.TesseractNotFoundError:
            raise RuntimeError(
                "Tesseract is not installed or not in your PATH. "
                "You may need to set the 'tesseract_cmd' in your config."
            )
        except Exception as e:
            # 可以添加日志记录来捕获其他潜在的错误
            return f"Error during OCR: {e}"